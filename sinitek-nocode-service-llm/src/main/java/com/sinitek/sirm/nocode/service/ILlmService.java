package com.sinitek.sirm.nocode.service;

import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatSqlDTO;
import java.io.File;
import java.util.List;

/**
 * 大语言模型服务接口
 *
 * <p>提供与大语言模型交互的核心功能，包括聊天对话、智能代理和文件上传等服务。
 * 支持多种LLM平台的统一接口访问，通过工厂模式实现不同平台的服务实例化。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>聊天对话：支持文本和图像输入的对话功能</li>
 *   <li>智能代理：基于Agent模式的高级AI交互</li>
 *   <li>文件上传：支持向LLM平台上传文件资源</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2024/4/23
 * @version 1.1
 */
public interface ILlmService {

    /**
     * 聊天对话模式调用大语言模型
     *
     * <p>支持文本和图像输入的对话功能，适用于常规的问答、文本生成等场景。
     * 可以处理单轮或多轮对话，支持上下文记忆。</p>
     *
     * @param request 聊天请求参数，包含提示词、应用ID、平台信息等
     * @return 聊天响应结果，包含生成的文本、token消耗、耗时等信息
     * @throws IllegalArgumentException 当请求参数为空或无效时
     * @throws RuntimeException 当LLM服务调用失败时
     */
    LlmChatCompletionsResponseDTO chatCompletions(LlmChatCompletionsRequestDTO request);

    /**
     * 智能代理模式调用大语言模型
     *
     * <p>基于Agent架构的高级AI交互模式，支持工具调用、函数执行、
     * 多步推理等复杂任务。适用于需要智能决策和自动化执行的场景。</p>
     *
     * @param request 代理请求参数，包含代理ID、提示词、参数等
     * @return 代理响应结果，包含执行结果、完成状态、中间过程等
     * @throws IllegalArgumentException 当请求参数为空或代理ID不存在时
     * @throws RuntimeException 当代理服务调用失败时
     */
    LlmAgentCompletionsResponseDTO agentCompletions(LlmAgentCompletionsRequestDTO request);

    /**
     * 上传文件到LLM平台
     *
     * <p>将本地文件上传到LLM平台的文件存储服务，获取文件ID用于后续的
     * 对话或代理调用中引用。支持图像、文档等多种文件类型。</p>
     *
     * @param file 要上传的文件对象，不能为null
     * @return 上传成功后返回的文件ID，用于在请求中引用该文件
     * @throws IllegalArgumentException 当文件为空或格式不支持时
     * @throws RuntimeException 当文件上传失败时
     */
    String uploadFile(File file);

    /**
     * 图片描述
     */
    String imageDesc(List<File> images);

    /**
     * 根据用户需求生成SQL查询语句
     *
     * <p>该方法会根据表单配置和用户需求生成相应的SQL查询语句。</p>
     *
     * @param request SQL生成请求，包含用户需求描述、表单代码和会话ID
     * @return 格式化后的SQL查询语句
     * @throws RuntimeException 当表单不存在、配置无效或LLM服务调用失败时
     */
    String formSqlGenerate(LlmChatSqlDTO request);

    /**
     * 根据用户需求生成Mermaid图表
     *
     * <p>该方法会根据用户的自然语言描述，通过大语言模型生成相应的Mermaid图表代码。
     * 支持流程图、时序图、类图等多种图表类型。</p>
     *
     * @param prompt 用户对图表的自然语言描述
     * @return 格式化后的Mermaid图表代码
     * @throws IllegalArgumentException 当提示词为空时
     * @throws RuntimeException 当LLM服务调用失败时
     */
    String mermaidGenerate(String prompt);
}
