package com.sinitek.sirm.nocode.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.constant.LlmGeneratorConstant;
import com.sinitek.sirm.nocode.constant.LlmMessageConstant;
import com.sinitek.sirm.nocode.constant.LlmPlatformConstant;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatSqlDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.service.ILlmService;
import com.sinitek.sirm.nocode.util.LlmTextUtil;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Slf4j
@Service
public class LlmServiceImpl implements ILlmService {

    /** 快速模型服务器地址 */
    @Value("${llm.sinitek-chat-fast.server:}")
    private String fastModelServer;

    /** 标准模型服务器地址 */
    @Value("${llm.sinitek-chat.server:}")
    private String standardModelServer;

    /** 快速模型API密钥 */
    @Value("${llm.sinitek-chat-fast.api-key:}")
    private String fastModelApiKey;

    /** 标准模型API密钥 */
    @Value("${llm.sinitek-chat.api-key:}")
    private String standardModelApiKey;

    /** Schema生成代理API密钥 */
    @Value("${llm.schema-gen-agent.api-key:}")
    private String schemaGenAgentApiKey;

    /** Schema生成代理服务器地址 */
    @Value("${llm.schema-gen-agent.server:}")
    private String schemaGenAgentServer;

    /** 教程生成代理API密钥 */
    @Value("${llm.manual-agent.api-key:}")
    private String manualAgentApiKey;

    /** 教程生成代理服务器地址 */
    @Value("${llm.manual-agent.server:}")
    private String manualAgentServer;

    /** Sql生成API密钥 */
    @Value("${llm.sql-gen-chat.api-key:}")
    private String sqlGenChatApiKey;

    /** Sql生成服务器地址 */
    @Value("${llm.sql-gen-chat.server:}")
    private String sqlGenChatServer;

    /** Sql生成API密钥 */
    @Value("${llm.diagram-gen-chat.api-key:}")
    private String diagramGenChatApiKey;

    /** Sql生成服务器地址 */
    @Value("${llm.diagram-gen-chat.server:}")
    private String diagramGenChatServer;

    @Autowired
    private IZdPageFormService pageFormService;

    @Autowired
    private IZdPageFormConfigService pageFormConfigService;

    /**
     * 执行阻塞式HTTP调用
     *
     * @param prompt 提示词内容
     * @param sessionId 会话ID（可选）
     * @param url 请求URL
     * @param apiKey API密钥
     * @return 响应字符串
     * @throws BussinessException 当提示词为空或请求失败时
     */
    private static String performBlockingCall(String prompt, Map<String,Object> param, String sessionId, String url, String apiKey) {
        if (StringUtils.isBlank(prompt)) {
            throw new BussinessException(LlmMessageConstant.PROMPT_MISSING);
        }

        // 构建请求参数
        Map<String, Object> requestParams = buildRequestParams(prompt, param, sessionId, "blocking");

        // 记录请求信息
        log.debug("发送阻塞式请求到: {}", url);
        log.trace("请求参数: {}", JsonUtil.toJsonString(requestParams));

        // 发送HTTP请求
        HttpRequest httpRequest = HttpRequest.post(url)
            .header("Authorization", "Bearer " + apiKey)
            .header("Content-Type", "application/json")
            .body(JsonUtil.toJsonString(requestParams));

        HttpResponse response = httpRequest.execute();
        return response.body();
    }

    /**
     * 构建请求参数
     *
     * @param prompt 提示词
     * @param sessionId 会话ID
     * @param responseMode 响应模式
     * @return 请求参数Map
     */
    private static Map<String, Object> buildRequestParams(String prompt,Map<String,Object> param, String sessionId, String responseMode) {
        Map<String, Object> params = new HashMap<>();
        params.put("response_mode", responseMode);
        params.put("user", "sinitek");
        params.put("inputs", param==null?new HashMap<>():param);
        params.put("query", prompt);

        if (StringUtils.isNotBlank(sessionId)) {
            params.put("conversation_id", sessionId);
        }

        return params;
    }

    @Override
    public LlmChatCompletionsResponseDTO chatCompletions(LlmChatCompletionsRequestDTO request) {
        long startTime = System.currentTimeMillis();

        // 选择模型服务器和API密钥
        String serverUrl, apiKey;
        if (request.isFastLlm() && StringUtils.isNotBlank(fastModelServer)) {
            serverUrl = fastModelServer + LlmPlatformConstant.MESSAGE_URL;
            apiKey = fastModelApiKey;
            log.debug("使用快速模型进行聊天对话");
        } else {
            serverUrl = standardModelServer + LlmPlatformConstant.MESSAGE_URL;
            apiKey = standardModelApiKey;
            log.debug("使用标准模型进行聊天对话");
        }

        // 调用阻塞式API
        String responseString = performBlockingCall(request.getPrompt(), null,null, serverUrl, apiKey);
        log.debug("收到大模型原始响应，长度: {}", responseString.length());

        // 解析响应JSON
        JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseString));

        // 构建响应对象
        return buildChatResponse(responseJson, startTime);
    }

    /**
     * 构建聊天响应对象
     *
     * @param responseJson 原始响应JSON
     * @param startTime 开始时间
     * @return 聊天响应DTO
     */
    private LlmChatCompletionsResponseDTO buildChatResponse(JsonNode responseJson, long startTime) {
        LlmChatCompletionsResponseDTO result = new LlmChatCompletionsResponseDTO();

        // 提取并清理回答内容
        String answer = responseJson.get("answer").asText();
        answer = cleanThinkTags(answer);

        // 设置基本信息
        result.setRequestId(responseJson.get("id").asText());
        result.setText(answer);
        result.setTime(System.currentTimeMillis() - startTime);

        // 设置Token使用统计
        if (responseJson.has("metadata")) {
            JsonNode usage = responseJson.get("metadata").get("usage");
            result.setInputTokens(usage.get("prompt_tokens").asInt());
            result.setOutputTokens(usage.get("completion_tokens").asInt());
        }

        log.info("聊天对话完成，耗时: {}ms, 输入tokens: {}, 输出tokens: {}",
            result.getTime(), result.getInputTokens(), result.getOutputTokens());

        return result;
    }

    /**
     * 清理回答中的思考标签
     *
     * @param answer 原始回答
     * @return 清理后的回答
     */
    private String cleanThinkTags(String answer) {
        if (StringUtils.isBlank(answer)) {
            return answer;
        }
        // 移除<think></think>标签及其内容
        return answer.replaceAll("(?s)<think>.*?</think>", "");
    }

    /**
     * 验证代理请求参数
     *
     * @param request 代理请求
     * @throws BussinessException 当参数无效时
     */
    private void validateAgentRequest(LlmAgentCompletionsRequestDTO request) {
        if (request == null) {
            throw new BussinessException("代理请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getAgentId())) {
            throw new BussinessException("代理ID不能为空");
        }
        if (StringUtils.isBlank(request.getPrompt())) {
            throw new BussinessException(LlmMessageConstant.PROMPT_MISSING);
        }
    }

    /**
     * 获取代理配置
     *
     * @param agentId 代理ID
     * @return 代理配置
     * @throws BussinessException 当代理不存在时
     */
    private AgentConfig getAgentConfig(String agentId) {
        if (LlmGeneratorConstant.AGENT_GEN_SCHEMA.equals(agentId)) {
            return new AgentConfig(schemaGenAgentServer, schemaGenAgentApiKey);
        }else if (LlmGeneratorConstant.AGENT_MANUAL.equals(agentId)) {
            return new AgentConfig(manualAgentServer, manualAgentApiKey);
        } else {
            throw new BussinessException(LlmMessageConstant.LLM_NOT_EXIST);
        }
    }

    /**
     * 构建代理请求参数
     *
     * @param request 代理请求
     * @return 请求参数Map
     */
    private Map<String, Object> buildAgentRequestParams(LlmAgentCompletionsRequestDTO request) {
        Map<String, Object> params = new HashMap<>();
        params.put("response_mode", "streaming");
        params.put("user", "sinitek");
        params.put("inputs", request.getParams() != null ? request.getParams() : new HashMap<>());
        String query = request.getPrompt();
        if(request.getWebInfo() != null){
            query = query + "\n\n 可用的web服务信息：" + request.getWebInfo();
        }
        if(request.getEnums() != null){
            query = query + "\n\n 可用的枚举信息：" + request.getEnums();
        }
        if(request.getModels() != null){
            query = query + "\n\n 可用的模型：" + request.getModels();
        }
        if(request.getImageDesc() != null){
            query = query + "\n\n 用户上传的图片内容：" + request.getImageDesc();
        }
        params.put("query", query);

        if (StringUtils.isNotBlank(request.getSessionId())) {
            params.put("conversation_id", request.getSessionId());
        }

        return params;
    }

    @Override
    public LlmAgentCompletionsResponseDTO agentCompletions(LlmAgentCompletionsRequestDTO request) {
        // 验证请求参数
        validateAgentRequest(request);

        // 获取代理服务配置
        AgentConfig agentConfig = getAgentConfig(request.getAgentId());

        long startTime = System.currentTimeMillis();
        String requestUrl = agentConfig.getServerUrl() + LlmPlatformConstant.MESSAGE_URL;

        // 构建流式请求参数
        Map<String, Object> requestParams = buildAgentRequestParams(request);

        log.debug("发送智能代理请求到: {}, 代理ID: {}", requestUrl, request.getAgentId());
        log.trace("代理请求参数: {}", JsonUtil.toJsonString(requestParams));

        // 执行流式请求并处理响应
        return processStreamingResponse(requestUrl, agentConfig.getApiKey(), requestParams, startTime);
    }

    @Override
    public String uploadFile(File file) {
        return "";
    }

    @Override
    public String imageDesc(List<File> images) {
        return "";
    }

    /**
     * 根据表单配置和用户需求生成SQL查询语句
     * 
     * <p>该方法会根据表单代码获取表单配置信息，包括表结构和字段映射，
     * 然后结合用户的自然语言需求，通过大语言模型生成相应的SQL查询语句。</p>
     * 
     * <p>生成过程包括：</p>
     * <ul>
     *   <li>获取表单的数据表名称和结构信息</li>
     *   <li>构建包含表结构的提示词参数</li>
     *   <li>调用SQL生成专用的LLM服务</li>
     *   <li>清理和格式化返回的SQL语句</li>
     * </ul>
     *
     * @param request SQL生成请求，包含用户需求描述、表单代码和会话ID
     * @return 格式化后的SQL查询语句，已去除代码块标记和思考标签
     * @throws BussinessException 当表单不存在、配置无效或LLM服务调用失败时抛出
     * @since 1.0
     */
    @Override
    public String formSqlGenerate(LlmChatSqlDTO request) {
        log.info("开始生成SQL，表单代码: {}, 需求: {}", request.getFormCode(), request.getPrompt());
        
        try {
            // 1. 构建SQL生成所需的参数，包含表结构信息
            Map<String, Object> sqlGenParams = buildSqlGenerationParams(request);
            log.debug("SQL生成参数构建完成，表名: {}", sqlGenParams.get("tableName"));
            
            // 2. 调用专用的SQL生成服务
            String generatedSql = callSqlGenerationService(request, sqlGenParams);
            
            log.info("SQL生成完成，表单代码: {}, SQL长度: {}", request.getFormCode(), generatedSql.length());
            return generatedSql;
            
        } catch (Exception e) {
            log.error("SQL生成失败，表单代码: {}, 错误: {}", request.getFormCode(), e.getMessage(), e);
            throw new BussinessException("SQL生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建SQL生成所需的参数
     * 
     * <p>根据表单代码获取相关的表单配置和数据表信息，
     * 构建供LLM理解的表结构描述。</p>
     *
     * @param request SQL生成请求
     * @return 包含表名和表结构信息的参数Map
     * @throws BussinessException 当表单不存在或配置无效时抛出
     */
    private Map<String, Object> buildSqlGenerationParams(LlmChatSqlDTO request) {
        String formCode = request.getFormCode();
        
        // 获取表单配置信息
        ZdPageFormDTO formConfig = pageFormService.view(formCode);
        if (formConfig == null) {
            throw new BussinessException("表单不存在，表单代码: " + formCode);
        }
        
        // 获取数据表名称
        String tableName = pageFormConfigService.getTableNameByFormCode(formCode);
        if (StringUtils.isBlank(tableName)) {
            throw new BussinessException("未找到表单对应的数据表，表单代码: " + formCode);
        }
        
        // 构建参数
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", tableName);
        params.put("schema", formConfig.getPageData());
        
        log.debug("SQL生成参数构建完成 - 表名: {}, 表单配置已加载", tableName);
        return params;
    }

    /**
     * 调用SQL生成服务
     *
     * @param request 原始请求
     * @param sqlGenParams SQL生成参数
     * @return 格式化后的SQL语句
     */
    private String callSqlGenerationService(LlmChatSqlDTO request, Map<String, Object> sqlGenParams) {
        String requestUrl = sqlGenChatServer + LlmPlatformConstant.MESSAGE_URL;
        log.debug("发送SQL生成请求到: {}", requestUrl);
        
        // 调用阻塞式LLM服务
        String responseString = performBlockingCall(
            request.getPrompt(), 
            sqlGenParams, 
            request.getSessionId(), 
            requestUrl, 
            sqlGenChatApiKey
        );
        
        log.debug("收到SQL生成响应，响应长度: {}", responseString.length());
        
        // 解析响应并提取SQL
        return extractAndFormatSql(responseString);
    }

    /**
     * 从响应中提取并格式化SQL语句
     *
     * @param responseString 原始响应字符串
     * @return 清理和格式化后的SQL语句
     */
    private String extractAndFormatSql(String responseString) {
        try {
            // 解析JSON响应
            JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseString));
            
            if (!responseJson.has("answer")) {
                throw new BussinessException("LLM响应中缺少answer字段");
            }
            
            // 提取并清理SQL内容
            String rawSql = responseJson.get("answer").asText();
            String cleanedSql = cleanThinkTags(rawSql);
            String formattedSql = LlmTextUtil.formatToSQL(cleanedSql);
            
            if (StringUtils.isBlank(formattedSql)) {
                throw new BussinessException("生成的SQL为空");
            }
            
            log.debug("SQL提取和格式化完成，最终SQL: {}", formattedSql);
            return formattedSql;
            
        } catch (Exception e) {
            log.error("SQL提取和格式化失败，原始响应: {}", responseString, e);
            throw new BussinessException("SQL提取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据用户需求生成Mermaid图表
     * 
     * <p>该方法会根据用户的自然语言描述，通过大语言模型生成相应的Mermaid图表代码。
     * 支持流程图、时序图、类图等多种图表类型。</p>
     * 
     * <p>生成过程包括：</p>
     * <ul>
     *   <li>验证用户输入的提示词</li>
     *   <li>调用Mermaid图表生成专用的LLM服务</li>
     *   <li>清理和格式化返回的Mermaid代码</li>
     * </ul>
     *
     * @param prompt 用户对图表的自然语言描述
     * @return 格式化后的Mermaid图表代码，已去除代码块标记和思考标签
     * @throws BussinessException 当提示词为空或LLM服务调用失败时抛出
     * @since 1.0
     */
    @Override
    public String mermaidGenerate(String prompt) {
        log.info("开始生成Mermaid图表，用户需求: {}", prompt);
        
        try {
            // 1. 验证输入参数
            if (StringUtils.isBlank(prompt)) {
                throw new BussinessException(LlmMessageConstant.PROMPT_MISSING);
            }
            
            // 2. 调用专用的Mermaid图表生成服务
            String generatedMermaid = callMermaidGenerationService(prompt);
            
            log.info("Mermaid图表生成完成，代码长度: {}", generatedMermaid.length());
            return generatedMermaid;
            
        } catch (Exception e) {
            log.error("Mermaid图表生成失败，用户需求: {}, 错误: {}", prompt, e.getMessage(), e);
            throw new BussinessException("Mermaid图表生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用Mermaid图表生成服务
     *
     * @param prompt 用户需求描述
     * @return 格式化后的Mermaid图表代码
     */
    private String callMermaidGenerationService(String prompt) {
        String requestUrl = diagramGenChatServer + LlmPlatformConstant.MESSAGE_URL;
        log.debug("发送Mermaid图表生成请求到: {}", requestUrl);
        
        // 调用阻塞式LLM服务
        String responseString = performBlockingCall(
            prompt, 
            null, 
            null, 
            requestUrl, 
            diagramGenChatApiKey
        );
        
        log.debug("收到Mermaid图表生成响应，响应长度: {}", responseString.length());
        
        // 解析响应并提取Mermaid代码
        return extractAndFormatMermaid(responseString);
    }

    /**
     * 从响应中提取并格式化Mermaid图表代码
     *
     * @param responseString 原始响应字符串
     * @return 清理和格式化后的Mermaid图表代码
     */
    private String extractAndFormatMermaid(String responseString) {
        try {
            // 解析JSON响应
            JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseString));
            
            if (!responseJson.has("answer")) {
                throw new BussinessException("LLM响应中缺少answer字段");
            }
            
            // 提取并清理Mermaid内容
            String rawMermaid = responseJson.get("answer").asText();
            String cleanedMermaid = cleanThinkTags(rawMermaid);
            String formattedMermaid = LlmTextUtil.formatToMermaid(cleanedMermaid);
            
            if (StringUtils.isBlank(formattedMermaid)) {
                throw new BussinessException("生成的Mermaid图表为空");
            }
            
            log.debug("Mermaid图表提取和格式化完成，最终代码: {}", formattedMermaid);
            return formattedMermaid;
            
        } catch (Exception e) {
            log.error("Mermaid图表提取和格式化失败，原始响应: {}", responseString, e);
            throw new BussinessException("Mermaid图表提取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理流式响应
     *
     * @param requestUrl 请求URL
     * @param apiKey API密钥
     * @param requestParams 请求参数
     * @param startTime 开始时间
     * @return 代理响应结果
     */
    private LlmAgentCompletionsResponseDTO processStreamingResponse(String requestUrl, String apiKey,
        Map<String, Object> requestParams,
        long startTime) {
        // 发送流式HTTP请求
        HttpRequest httpRequest = HttpRequest.post(requestUrl)
            .header("Authorization", "Bearer " + apiKey)
            .header("Content-Type", "application/json")
            .body(JsonUtil.toJsonString(requestParams));

        // 初始化响应处理器
        StreamingResponseHandler handler = new StreamingResponseHandler();

        try {
            HttpResponse response = httpRequest.execute();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.bodyStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    handler.processLine(line);
                }
            } catch (IOException e) {
                log.error("读取流式响应失败", e);
                throw new BussinessException("读取流式响应失败", e);
            }
        } catch (Exception e) {
            log.error("流式请求失败", e);
            throw new BussinessException("流式请求失败", e);
        }

        // 构建最终响应
        return handler.buildFinalResponse(startTime);
    }

    /**
     * 代理配置内部类
     */
    private static class AgentConfig {
        private final String serverUrl;
        private final String apiKey;

        public AgentConfig(String serverUrl, String apiKey) {
            this.serverUrl = serverUrl;
            this.apiKey = apiKey;
        }

        public String getServerUrl() {
            return serverUrl;
        }

        public String getApiKey() {
            return apiKey;
        }
    }

    /**
     * 流式响应处理器
     */
    private static class StreamingResponseHandler {
        private final StringBuilder fullResponse = new StringBuilder();
        private String requestId;
        private String sessionId;
        private int inputTokens = 0;
        private int outputTokens = 0;

        /**
         * 处理单行响应数据
         */
        public void processLine(String line) {
            // 跳过空行
            if (StringUtils.isBlank(line)) {
                return;
            }

            // 处理SSE格式数据
            if (line.startsWith("data: ")) {
                String jsonStr = line.substring(6).trim();

                // 跳过特殊的SSE结束标志
                if ("[DONE]".equals(jsonStr)) {
                    return;
                }

                processJsonData(jsonStr);
            } else if (line.startsWith("{") && line.contains("\"code\"") && line.contains("\"message\"")) {
                // 处理可能的错误响应
                handlePossibleError(line);
            }
        }

        /**
         * 处理JSON数据
         */
        private void processJsonData(String jsonStr) {
            try {
                JsonNode jsonNode = JsonUtil.getValueToJsonNode(JsonUtil.toMap(jsonStr));

                // 检查错误响应
                checkForErrors(jsonNode, jsonStr);

                // 提取请求ID
                extractRequestId(jsonNode);

                // 处理事件内容
                processEventContent(jsonNode);

                // 提取会话ID
                extractSessionId(jsonNode);

                // 处理Token统计
                processTokenUsage(jsonNode);

            } catch (Exception e) {
                log.warn("解析流式响应数据失败，跳过此行: {} - 错误: {}", jsonStr, e.getMessage());
            }
        }

        /**
         * 检查错误响应
         */
        private void checkForErrors(JsonNode jsonNode, String jsonStr) {
            if (jsonNode.has("code") && jsonNode.has("message") && jsonNode.has("status")) {
                String status = jsonNode.get("status").asText();
                if ("error".equals(status) || "fail".equals(status)) {
                    throw new RuntimeException("大模型返回错误信息: " + jsonStr);
                }
            }
        }

        /**
         * 提取请求ID
         */
        private void extractRequestId(JsonNode jsonNode) {
            if (requestId == null) {
                if (jsonNode.has("id")) {
                    requestId = jsonNode.get("id").asText();
                } else if (jsonNode.has("message_id")) {
                    requestId = jsonNode.get("message_id").asText();
                } else if (jsonNode.has("task_id")) {
                    requestId = jsonNode.get("task_id").asText();
                }
            }
        }

        /**
         * 处理事件内容
         */
        private void processEventContent(JsonNode jsonNode) {
            String event = jsonNode.has("event") ? jsonNode.get("event").asText() : "";

            // 处理消息内容
            if (isMessageEvent(event)) {
                processMessageContent(jsonNode);
            }

            // 处理工作流节点内容
            if ("node_finished".equals(event)) {
                processWorkflowNodeContent(jsonNode);
            }
        }

        /**
         * 判断是否为消息事件
         */
        private boolean isMessageEvent(String event) {
            return "message".equals(event) || "agent_message".equals(event) ||
                "workflow_finished".equals(event) || "message_end".equals(event);
        }

        /**
         * 处理消息内容
         */
        private void processMessageContent(JsonNode jsonNode) {
            if (jsonNode.has("answer")) {
                String answer = jsonNode.get("answer").asText();
                if (StringUtils.isNotEmpty(answer)) {
                    answer = cleanThinkTags(answer);
                    fullResponse.append(answer);
                    log.debug("接收到流式响应片段: {}", answer);
                }
            }
        }

        /**
         * 处理工作流节点内容
         */
        private void processWorkflowNodeContent(JsonNode jsonNode) {
            if (jsonNode.has("data")) {
                JsonNode data = jsonNode.get("data");
                if (data.has("outputs") && data.get("outputs").has("text")) {
                    String text = data.get("outputs").get("text").asText();
                    if (StringUtils.isNotEmpty(text)) {
                        text = cleanThinkTags(text);
                        fullResponse.append(text);
                        log.debug("接收到工作流节点输出: {}", text);
                    }
                }
            }
        }

        /**
         * 提取会话ID
         */
        private void extractSessionId(JsonNode jsonNode) {
            if (sessionId == null && jsonNode.has("conversation_id")) {
                sessionId = jsonNode.get("conversation_id").asText();
            }
        }

        /**
         * 处理Token使用统计
         */
        private void processTokenUsage(JsonNode jsonNode) {
            if (jsonNode.has("metadata") && jsonNode.get("metadata").has("usage")) {
                JsonNode usage = jsonNode.get("metadata").get("usage");

                if (usage.has("prompt_tokens")) {
                    inputTokens = Math.max(inputTokens, usage.get("prompt_tokens").asInt());
                }
                if (usage.has("completion_tokens")) {
                    outputTokens = Math.max(outputTokens, usage.get("completion_tokens").asInt());
                }
                if (usage.has("total_tokens")) {
                    int totalTokens = usage.get("total_tokens").asInt();
                    if (totalTokens > 0 && inputTokens == 0 && outputTokens == 0) {
                        outputTokens = totalTokens;
                    }
                }
            }
        }

        /**
         * 处理可能的错误响应
         */
        private void handlePossibleError(String line) {
            try {
                JsonNode errorNode = JsonUtil.getValueToJsonNode(JsonUtil.toMap(line));
                if (errorNode.has("code") && errorNode.has("message")) {
                    throw new RuntimeException("大模型返回错误信息: " + line);
                }
            } catch (Exception e) {
                log.warn("解析可能的错误响应失败: {} - 错误: {}", line, e.getMessage());
            }
        }

        /**
         * 构建最终响应
         */
        public LlmAgentCompletionsResponseDTO buildFinalResponse(long startTime) {
            LlmAgentCompletionsResponseDTO result = new LlmAgentCompletionsResponseDTO();

            String responseText = fullResponse.toString();
            result.setRequestId(requestId);
            result.setText(responseText);
            result.setTime(System.currentTimeMillis() - startTime);
            result.setInputTokens(inputTokens);
            result.setOutputTokens(outputTokens);
            result.setSessionId(sessionId);

            // 判断是否完成
            result.setFinishFlag(isTaskCompleted(responseText) ? 1 : 0);

            log.info("智能代理响应完成，耗时: {}ms, 响应长度: {}, 内容预览: {}",
                result.getTime(), responseText.length(),
                responseText.length() > 100 ? responseText.substring(0, 100) + "..." : responseText);

            return result;
        }

        /**
         * 判断任务是否完成
         */
        private boolean isTaskCompleted(String responseText) {
            if (StringUtils.isEmpty(responseText)) {
                return false;
            }
            return responseText.contains("FinalAnswer") || responseText.contains("Final Answer")  || responseText.contains("//任务已完成") || responseText.contains("// 任务已完成") ||
                responseText.contains("function finalFunction(") || responseText.contains("function end(");
        }

        /**
         * 清理思考标签
         */
        private String cleanThinkTags(String text) {
            if (StringUtils.isBlank(text)) {
                return text;
            }
            return text.replaceAll("(?s)<think>.*?</think>", "");
        }
    }
}
