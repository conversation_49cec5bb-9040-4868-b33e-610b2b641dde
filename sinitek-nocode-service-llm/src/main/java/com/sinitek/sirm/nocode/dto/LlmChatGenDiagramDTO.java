package com.sinitek.sirm.nocode.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 智能体请求DTO
 *
 * <AUTHOR>
 * @since 2024/4/23
 */
@Data
@ApiModel(description = "智能体生成图表请求DTO")
public class LlmChatGenDiagramDTO {
    @ApiModelProperty("提示词")
    private String prompt;
    @ApiModelProperty("formCode")
    private String formCode;
    @ApiModelProperty("会话id")
    private String sessionId;
}
