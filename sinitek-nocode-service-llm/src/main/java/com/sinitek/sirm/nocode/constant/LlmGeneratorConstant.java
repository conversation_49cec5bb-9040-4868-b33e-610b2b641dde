package com.sinitek.sirm.nocode.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class LlmGeneratorConstant {

    @SuppressWarnings("squid:S1075")
    public static final String COMMON_TEMPLATE_PATH = "/template/llm/common/";

    @SuppressWarnings("squid:S1075")
    public static final String COMPONENT_TEMPLATE_PATH = "/template/llm/component/";

    @SuppressWarnings("squid:S1075")
    public static final String MODEL_TEMPLATE_PATH = "/template/llm/model/";

    @SuppressWarnings("squid:S1075")
    public static final String NORMAL_TEMPLATE_PATH = "/template/llm/normal/";

    public static final String AGENT_GEN_SCHEMA = "agent_gen_schema";

    public static final String AGENT_MANUAL = "agent_manual";

    /**
     * 当宽度大于此值时，调整为自动宽度
     */
    public static final int MIN_AUTO_WIDTH = 200;

    public static final String END_FUNCTION = "function end(schema){return schema}";
    public static final String ACTION_INPUT = "action_input";
    public static final int FINISH_FLAG_COMPLETE = 1;
}
