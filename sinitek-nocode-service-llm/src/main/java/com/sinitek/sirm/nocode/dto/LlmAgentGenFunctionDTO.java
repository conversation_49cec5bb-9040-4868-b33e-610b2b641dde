package com.sinitek.sirm.nocode.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 智能体请求DTO
 *
 * <AUTHOR>
 * @since 2024/4/23
 */
@Data
@ApiModel(description = "智能体function生成请求DTO")
public class LlmAgentGenFunctionDTO {
    @ApiModelProperty("提示词")
    private String prompt;
    @ApiModelProperty("schema")
    private String schema;
    @ApiModelProperty("会话id")
    private String sessionId;
}
