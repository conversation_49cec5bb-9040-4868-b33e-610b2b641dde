package com.sinitek.sirm.nocode.util;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 大模型返回文本工具类
 *
 * <AUTHOR>
 * @since 2024/5/14
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class LlmTextUtil {
    private static final String DEFAULT_JSON = "json";

    private static final String DEFAULT_SQL = "sql";

    private static final String DEFAULT_HTML = "html";

    private static final String DEFAULT_JS = "javascript";

    private static final String DEFAULT_MERMAID = "mermaid";

    private static final int DEFAULT_SUBSTRING_ADD_NUM_SQL = DEFAULT_SQL.length() + 3;

    private static final int DEFAULT_SUBSTRING_ADD_NUM_MERMAID = DEFAULT_MERMAID.length() + 3;

    private static final int DEFAULT_SUBSTRING_ADD_NUM = 7;

    private static final int DEFAULT_SUBSTRING_ADD_NUM_JS = DEFAULT_JS.length() + 3;




    public static String formatToJson(String text) {
        text = text.trim();
        if (text.contains("```json")) {
            text = text.substring(text.indexOf("```json") + DEFAULT_SUBSTRING_ADD_NUM);
        }
        if (text.contains("```")) {
            text = text.substring(0,
                text.lastIndexOf("```"));
        }
        text = text.trim();
        //如果开头是"json"，则去除"json"字符串
        if (text.startsWith(DEFAULT_JSON)) {
            text = text.substring(DEFAULT_JSON.length());
        }
        text = text.trim();
        //逐行检查，如果行里包含//则要去除//以及后面的字符
        String[] lines = text.split("\n");
        for (int i = 0; i < lines.length; i++) {
            if (lines[i].contains("//")) {
                lines[i] = lines[i].substring(0, lines[i].indexOf("//"));
            }
        }
        text = String.join("\n", lines).trim();
        return text;
    }

    public static String formatToJS(String text) {
        text = text.trim();
        if (text.contains("```javascript")) {
            text = text.substring(text.indexOf("```javascript") + DEFAULT_SUBSTRING_ADD_NUM_JS);
        }
        if (text.contains("```")) {
            text = text.substring(0,
                text.lastIndexOf("```"));
        }
        text = text.trim();
        if (text.startsWith(DEFAULT_JS)) {
            text = text.substring(DEFAULT_JS.length());
        }
        return text.trim();
    }

    public static String formatToSQL(String text) {
        text = text.trim();
        if (text.contains("```sql")) {
            text = text.substring(text.indexOf("```sql") + DEFAULT_SUBSTRING_ADD_NUM_SQL);
        }
        if (text.contains("```")) {
            text = text.substring(0,
                text.lastIndexOf("```"));
        }
        text = text.trim();
        if (text.startsWith(DEFAULT_SQL)) {
            text = text.substring(DEFAULT_SQL.length());
        }
        return text.trim();
    }

    public static String formatToMermaid(String text) {
        text = text.trim();
        if (text.contains("```mermaid")) {
            text = text.substring(text.indexOf("```mermaid") + DEFAULT_SUBSTRING_ADD_NUM_MERMAID);
        }
        if (text.contains("```")) {
            text = text.substring(0,
                text.lastIndexOf("```"));
        }
        text = text.trim();
        if (text.startsWith(DEFAULT_MERMAID)) {
            text = text.substring(DEFAULT_MERMAID.length());
        }
        return text.trim();
    }
}
