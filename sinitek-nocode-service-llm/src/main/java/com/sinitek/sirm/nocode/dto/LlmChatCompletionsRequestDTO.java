package com.sinitek.sirm.nocode.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.File;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 大模型请求DTO
 *
 * <AUTHOR>
 * @since 2024/4/23
 */
@Data
@ApiModel(description = "大模型请求DTO")
public class LlmChatCompletionsRequestDTO {

    @ApiModelProperty("appId")
    private String appId;
    @ApiModelProperty("提示词")
    private String prompt;
    @ApiModelProperty("消息")
    private List<Map<String, Object>> messages;
    @ApiModelProperty("会话id")
    private String sessionId;
    @ApiModelProperty("返回最大token数")
    private Integer maxTokens;
    @SuppressWarnings("squid:DTOFloatCheck")
    @ApiModelProperty("temperature值")
    private Float temperature;
    @SuppressWarnings("squid:DTOFloatCheck")
    @ApiModelProperty("topP值")
    private Double topP;
    @ApiModelProperty("图片")
    private List<File> images;
    @ApiModelProperty("是否调用更快的大模型")
    private boolean fastLlm;
}
