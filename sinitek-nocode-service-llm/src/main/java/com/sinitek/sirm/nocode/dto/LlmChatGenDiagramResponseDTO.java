package com.sinitek.sirm.nocode.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 大模型应答DTO
 *
 * <AUTHOR>
 * @since 2024/4/23
 */
@Data
@ApiModel(description = "大模型应答DTO")
public class LlmChatGenDiagramResponseDTO {
    @ApiModelProperty("requestId")
    private String requestId;
    @ApiModelProperty("回复内容片段")
    private String text;
    @ApiModelProperty("输入token数")
    private Integer inputTokens = 0;
    @ApiModelProperty("输出token数")
    private Integer outputTokens = 0;
    @ApiModelProperty("调用耗时,毫秒")
    private Long time = 0L;
    @ApiModelProperty("sessionId")
    private String sessionId;
}
