package com.sinitek.sirm.nocode.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.fasterxml.jackson.databind.JsonNode;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.constant.LlmMessageConstant;
import com.sinitek.sirm.nocode.constant.LlmPlatformConstant;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmAgentCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsRequestDTO;
import com.sinitek.sirm.nocode.dto.LlmChatCompletionsResponseDTO;
import com.sinitek.sirm.nocode.dto.LlmChatSqlDTO;
import com.sinitek.sirm.nocode.service.ILlmService;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 携宁内网大语言模型图像处理服务实现
 *
 * <p>基于携宁内网部署的支持图像输入的大语言模型平台，提供多模态对话功能。
 * 支持图像上传、图像理解和基于图像内容的智能对话。</p>
 *
 * <p>主要特性：</p>
 * <ul>
 *   <li>图像上传：支持多种格式的图像文件上传</li>
 *   <li>图像理解：基于视觉模型的图像内容分析</li>
 *   <li>多模态对话：结合文本和图像的智能对话</li>
 *   <li>内网部署：确保图像数据安全和隐私保护</li>
 * </ul>
 *
 * <p>适用场景：</p>
 * <ul>
 *   <li>界面设计稿分析和代码生成</li>
 *   <li>图像内容描述和理解</li>
 *   <li>基于图像的业务流程分析</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2024/4/23
 * @version 1.1
 */
@Slf4j
@Service
public class LlmWithImageServiceImpl implements ILlmService {

    /** 图像处理模型服务器地址 */
    @Value("${llm.sinitek-image.server:}")
    private String imageModelServer;

    /** 图像处理模型API密钥 */
    @Value("${llm.sinitek-image.api-key:}")
    private String imageModelApiKey;

    @Override
    public LlmChatCompletionsResponseDTO chatCompletions(LlmChatCompletionsRequestDTO request) {
        // 验证请求参数
        validateChatRequest(request);

        long startTime = System.currentTimeMillis();
        String requestUrl = imageModelServer + LlmPlatformConstant.MESSAGE_URL;

        // 构建多模态请求参数
        Map<String, Object> requestParams = buildMultimodalRequestParams(request);

        log.debug("发送图像处理请求到: {}", requestUrl);
        log.trace("图像处理请求参数: {}", JsonUtil.toJsonString(requestParams));

        // 发送HTTP请求
        HttpRequest httpRequest = HttpRequest.post(requestUrl)
            .header("Authorization", "Bearer " + imageModelApiKey)
            .header("Content-Type", "application/json")
            .body(JsonUtil.toJsonString(requestParams));

        HttpResponse response = httpRequest.execute();
        String responseString = response.body();

        log.debug("收到图像处理响应，长度: {}", responseString.length());

        // 解析并构建响应
        JsonNode responseJson = JsonUtil.getValueToJsonNode(JsonUtil.toMap(responseString));
        return buildImageChatResponse(responseJson, responseString, startTime);
    }

    /**
     * 验证聊天请求参数
     *
     * @param request 聊天请求
     * @throws BussinessException 当参数无效时
     */
    private void validateChatRequest(LlmChatCompletionsRequestDTO request) {
        if (request == null) {
            throw new BussinessException("请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getPrompt())) {
            throw new BussinessException(LlmMessageConstant.PROMPT_MISSING);
        }
        if (request.getImages() == null) {
            throw new BussinessException("图像文件不能为空");
        }
    }

    /**
     * 构建多模态请求参数
     *
     * @param request 聊天请求
     * @return 请求参数Map
     */
    private Map<String, Object> buildMultimodalRequestParams(LlmChatCompletionsRequestDTO request) {
        Map<String, Object> params = new HashMap<>();
        params.put("response_mode", "blocking");
        params.put("user", "sinitek");
        params.put("inputs", new HashMap<>());
        params.put("query", request.getPrompt());

        // 处理图像文件
        List<Map<String, Object>> files = new ArrayList<>();

        // 上传图像并获取文件ID
        for (File image : request.getImages()) {
            String fileId= uploadFile(image);
            Map<String, Object> imageFile = new HashMap<>();
            imageFile.put("type", "image");
            imageFile.put("transfer_method", "local_file");
            files.add(imageFile);
            imageFile.put("upload_file_id", fileId);
        }
        params.put("files", files);

        return params;
    }

    /**
     * 构建图像聊天响应
     *
     * @param responseJson 响应JSON
     * @param responseString 原始响应字符串
     * @param startTime 开始时间
     * @return 聊天响应DTO
     */
    private LlmChatCompletionsResponseDTO buildImageChatResponse(JsonNode responseJson,
                                                                String responseString,
                                                                long startTime) {
        LlmChatCompletionsResponseDTO result = new LlmChatCompletionsResponseDTO();

        // 设置请求ID
        if (responseJson.has("id")) {
            result.setRequestId(responseJson.get("id").asText());
        }

        // 设置响应内容
        if (responseJson.has("answer")) {
            String answer = responseJson.get("answer").asText();
            if (StringUtils.isNotBlank(answer)) {
                result.setText(answer);
                log.info("图像处理完成，响应长度: {}", answer.length());
            } else {
                result.setText(responseString);
                log.warn("未获取到有效的answer字段，使用原始响应");
            }
        } else {
            result.setText(responseString);
            log.warn("响应中缺少answer字段，使用原始响应");
        }

        // 设置耗时
        result.setTime(System.currentTimeMillis() - startTime);

        // 设置Token使用统计（如果有的话）
        if (responseJson.has("metadata") && responseJson.get("metadata").has("usage")) {
            JsonNode usage = responseJson.get("metadata").get("usage");
            if (usage.has("prompt_tokens")) {
                result.setInputTokens(usage.get("prompt_tokens").asInt());
            }
            if (usage.has("completion_tokens")) {
                result.setOutputTokens(usage.get("completion_tokens").asInt());
            }
        }

        log.info("图像聊天对话完成，耗时: {}ms, 输入tokens: {}, 输出tokens: {}",
                result.getTime(), result.getInputTokens(), result.getOutputTokens());

        return result;
    }

    @Override
    public LlmAgentCompletionsResponseDTO agentCompletions(LlmAgentCompletionsRequestDTO request) {
        // 图像处理服务暂不支持Agent模式
        log.warn("图像处理服务暂不支持智能代理模式，代理ID: {}",
                request != null ? request.getAgentId() : "null");
        throw new BussinessException("图像处理服务暂不支持智能代理模式");
    }

    @Override
    public String uploadFile(File file) {
        // 验证文件参数
        validateUploadFile(file);

        String uploadUrl = imageModelServer + LlmPlatformConstant.UPLOAD_FILE;

        log.debug("上传文件到: {}, 文件名: {}, 大小: {} bytes",
                uploadUrl, file.getName(), file.length());

        // 构建文件上传请求
        HttpRequest uploadRequest = HttpRequest.post(uploadUrl)
            .header("Authorization", "Bearer " + imageModelApiKey)
            .header("Content-Type", "multipart/form-data")
            .form("file", file);

        HttpResponse response = uploadRequest.execute();

        // 处理上传响应
        return handleUploadResponse(response, file.getName());
    }

    @Override
    public String imageDesc(List<File> images){
        LlmChatCompletionsRequestDTO request = new LlmChatCompletionsRequestDTO();
        request.setPrompt("详细请描述一下这几张图片，最后返回一个json数组的数据");
        request.setImages(images);
        LlmChatCompletionsResponseDTO response = chatCompletions(request);
        return response.getText();
    }

    @Override
    public String formSqlGenerate(LlmChatSqlDTO request) {
        return "";
    }

    @Override
    public String mermaidGenerate(String prompt) {
        // 图像处理服务暂不支持Mermaid图表生成
        log.warn("图像处理服务暂不支持Mermaid图表生成功能，提示词: {}", prompt);
        throw new BussinessException("图像处理服务暂不支持Mermaid图表生成功能");
    }

    /**
     * 验证上传文件
     *
     * @param file 要上传的文件
     * @throws BussinessException 当文件无效时
     */
    private void validateUploadFile(File file) {
        if (file == null) {
            throw new BussinessException("上传文件不能为空");
        }
        if (!file.exists()) {
            throw new BussinessException("上传文件不存在");
        }
        if (!file.isFile()) {
            throw new BussinessException("上传对象必须是文件");
        }
        if (file.length() == 0) {
            throw new BussinessException("上传文件不能为空文件");
        }

        // 检查文件类型（可选）
        String fileName = file.getName().toLowerCase();
        if (!isValidImageFile(fileName)) {
            log.warn("上传的文件可能不是有效的图像文件: {}", fileName);
        }
    }

    /**
     * 检查是否为有效的图像文件
     *
     * @param fileName 文件名
     * @return 是否为有效图像文件
     */
    private boolean isValidImageFile(String fileName) {
        String[] validExtensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"};
        for (String ext : validExtensions) {
            if (fileName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理文件上传响应
     *
     * @param response HTTP响应
     * @param fileName 文件名
     * @return 文件ID
     * @throws BussinessException 当上传失败时
     */
    private String handleUploadResponse(HttpResponse response, String fileName) {
        if (response.getStatus() == HttpStatus.HTTP_CREATED) {
            String responseString = response.body();
            log.debug("文件上传成功，响应: {}", responseString);

            try {
                Map<String, Object> responseMap = JsonUtil.toMap(responseString);
                String fileId = responseMap.get("id").toString();

                log.info("文件上传成功，文件名: {}, 文件ID: {}", fileName, fileId);
                return fileId;

            } catch (Exception e) {
                log.error("解析文件上传响应失败: {}", responseString, e);
                throw new BussinessException("解析文件上传响应失败");
            }
        } else {
            log.error("文件上传失败，状态码: {}, 响应: {}",
                    response.getStatus(), response.body());
            throw new BussinessException(LlmMessageConstant.UPLOAD_FILE_FAILURE);
        }
    }
}
