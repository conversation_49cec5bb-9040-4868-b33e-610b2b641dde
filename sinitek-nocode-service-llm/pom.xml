<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <artifactId>sinitek-nocode-service-llm</artifactId>

    <dependencies>
        <dependency>
            <artifactId>sinitek-nocode-api</artifactId>
            <groupId>com.sinitek.sirm</groupId>
        </dependency>

        <dependency>
            <artifactId>sinitek_commoncore</artifactId>
            <groupId>com.sinitek.sinicube</groupId>
        </dependency>

        <dependency>
            <artifactId>sinitek-cloud-base</artifactId>
            <groupId>com.sinitek.sinicube</groupId>
        </dependency>


        <dependency>
            <artifactId>sinitek-cloud-sirmapp-sdk</artifactId>
            <groupId>com.sinitek.sinicube</groupId>
        </dependency>

        <dependency>
            <artifactId>sinitek-nocode-dal</artifactId>
            <groupId>com.sinitek.sirm</groupId>
        </dependency>

        <dependency>
            <artifactId>postgresql</artifactId>
            <groupId>org.postgresql</groupId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <artifactId>spring-boot-starter-test</artifactId>
            <groupId>org.springframework.boot</groupId>
            <scope>test</scope>
        </dependency>
        <!-- <dependency>
           <artifactId>swagger-annotations</artifactId>
           <groupId>io.swagger</groupId>
           <scope>compile</scope>
           <version>1.6.8</version>
         </dependency>-->
    </dependencies>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>

    <parent>
        <artifactId>sinitek-nocode</artifactId>
        <groupId>com.sinitek.sirm</groupId>
        <version>${revision}</version>
    </parent>

    <properties>
        <skipTests>false</skipTests>
    </properties>
</project>
