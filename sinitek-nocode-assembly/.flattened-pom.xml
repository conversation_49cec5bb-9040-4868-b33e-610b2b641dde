<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sinitek.sirm</groupId>
    <artifactId>sinitek-nocode</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>com.sinitek.sirm</groupId>
  <artifactId>sinitek-nocode-assembly</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>${pom.package}</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <skip.jar>false</skip.jar>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.deploy.skip>true</maven.deploy.skip>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.sinitek.sirm</groupId>
      <artifactId>sinitek-nocode-service-design</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sinitek.sirm</groupId>
      <artifactId>sinitek-nocode-service-runtime</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sinitek.sirm</groupId>
      <artifactId>sinitek-nocode-service-llm</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-spring</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-junit4</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
          <showWarnings>true</showWarnings>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <layout>ZIP</layout>
          <skip>${skip.jar}</skip>
          <mainClass>com.sinitek.sirm.nocode.SirmApplication</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-war-plugin</artifactId>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
          <warName>${project.artifactId}</warName>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>jar</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <pom.package>jar</pom.package>
      </properties>
    </profile>
    <profile>
      <id>war</id>
      <properties>
        <skip.jar>true</skip.jar>
        <pom.package>war</pom.package>
      </properties>
      <dependencies>
        <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
          <scope>provided</scope>
        </dependency>
      </dependencies>
    </profile>
  </profiles>
</project>
