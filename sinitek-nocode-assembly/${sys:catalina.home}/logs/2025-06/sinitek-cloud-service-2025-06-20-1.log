[INFO][SIRM][92333,1,main][2025-06-20 16:55:10,734][com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[INFO][SIRM][92333,33,background-preinit][2025-06-20 16:55:10,759][org.hibernate.validator.internal.util.Version.<clinit>:21] - HV000001: Hibernate Validator 6.2.5.Final
[INFO][SIRM][92333,1,main][2025-06-20 16:55:11,676][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][92333,1,main][2025-06-20 16:55:11,676][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[WARN][SIRM][92333,1,main][2025-06-20 16:55:14,006][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend] & group[DEFAULT_GROUP]
[WARN][SIRM][92333,1,main][2025-06-20 16:55:14,154][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend-local.yml] & group[DEFAULT_GROUP]
[INFO][SIRM][92333,1,main][2025-06-20 16:55:14,154][org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.doInitialize:134] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP'}]
[INFO][SIRM][92333,1,main][2025-06-20 16:55:14,163][org.springframework.boot.SpringApplication.logStartupProfileInfo:651] - The following 1 profile is active: "local"
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,066][org.springframework.cloud.context.scope.GenericScope.setSerializationId:283] - BeanFactory id=759510ee-e750-3857-b86e-d0f43501cdbc
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,419][com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39] - Post-processing PropertySource instances
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,429][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,430][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,431][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,460][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,461][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,461][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,462][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,469][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,469][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,469][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,477][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,478][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,478][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap-local.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,483][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,484][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,524][com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2:31] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,574][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'hibernateValidateConfig' of type [com.sinitek.sirm.config.HibernateValidateConfig$$EnhancerBySpringCGLIB$$12f463c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,574][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'springMessageConfig' of type [com.sinitek.sirm.config.SpringMessageConfig$$EnhancerBySpringCGLIB$$fba513b6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,589][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,590][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,590][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda/0x0000400001684cb8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,605][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,618][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,618][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,642][com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2:35] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,647][com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2:35] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,676][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'i18nProperties' of type [com.sinitek.sirm.i18n.properties.I18nProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,729][com.sinitek.sirm.config.SpringMessageConfig.messageSource:52] - 加载默认消息资源文件：classpath*:message/messages-*.properties
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,732][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'messageSource' of type [org.springframework.context.support.ReloadableResourceBundleMessageSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:17,769][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'validator' of type [org.springframework.validation.beanvalidation.LocalValidatorFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:18,634][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'cacheConfig' of type [com.sinitek.sirm.config.CacheConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:19,488][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108] - Tomcat initialized with port(s): 8097 (http)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:19,843][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:290] - Root WebApplicationContext: initialization completed in 5655 ms
[INFO][SIRM][92333,1,main][2025-06-20 16:55:20,345][com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55] - Init DruidDataSource
[INFO][SIRM][92333,1,main][2025-06-20 16:55:22,727][com.alibaba.druid.pool.DruidDataSource.init:972] - {dataSource-1} inited
[INFO][SIRM][92333,1,main][2025-06-20 16:55:26,424][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.<init>:38] - flyway jdbcUrl: **********************************************************?useUnicode=true&characterEncoding=utf8&currentSchema=public&stringtype=unspecified
[INFO][SIRM][92333,1,main][2025-06-20 16:55:26,933][com.sinitek.sirm.ip.limit.interceptor.IpWhiteListInterceptor.init:56] - 当前ip限制状态为: false, true为开启状态,false为关闭状态
[INFO][SIRM][92333,1,main][2025-06-20 16:55:27,784][com.sinitek.sirm.config.SpringMessageConfig.localeChangeInterceptor:123] - 国际化开启状态: false, 系统默认的语言环境为: zh_CN,当前系统支持的语言环境列表: [LocaleInfo(locale=zh_CN, localeName=简体中文, localeChineseName=简体中文), LocaleInfo(locale=zh_HK, localeName=繁體中文, localeChineseName=繁体中文)]
[INFO][SIRM][92333,1,main][2025-06-20 16:55:28,091][springfox.documentation.spring.web.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[INFO][SIRM][92333,1,main][2025-06-20 16:55:28,470][com.sinitek.cloud.base.config.CloudSiniCubeConfig.currentUserInfo:37] - 当前com.sinitek.sirm.common.user.bridging.ICurrentUserInfo的实现类为: com.sinitek.cloud.base.user.CloudServerCurrentUserInfoImpl
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,096][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:46] - 开始检查有没有执行错误的脚本，如果有则先清理掉
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,100][com.zaxxer.hikari.HikariDataSource.getConnection:110] - HikariPool-1 - Starting...
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,438][com.zaxxer.hikari.HikariDataSource.getConnection:123] - HikariPool-1 - Start completed.
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,481][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,481][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Database: ********************************************************** (PostgreSQL 13.2)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,738][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Repair of failed migration in Schema History table "public"."flyway_schema_history" not necessary. No failed migration detected.
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,812][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Successfully repaired schema history table "public"."flyway_schema_history" (execution time 00:00.149s).
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,870][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:48] - 开始执行SQL脚本
[INFO][SIRM][92333,1,main][2025-06-20 16:55:30,894][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][92333,1,main][2025-06-20 16:55:31,288][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Current version of schema "public": 100.20250605.01
[INFO][SIRM][92333,1,main][2025-06-20 16:55:31,312][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Schema "public" is up to date. No migration necessary.
[INFO][SIRM][92333,1,main][2025-06-20 16:55:31,830][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][92333,1,main][2025-06-20 16:55:31,830][com.zaxxer.hikari.HikariDataSource.close:350] - HikariPool-1 - Shutdown initiated...
[INFO][SIRM][92333,1,main][2025-06-20 16:55:31,944][com.zaxxer.hikari.HikariDataSource.close:352] - HikariPool-1 - Shutdown completed.
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,122][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220] - Tomcat started on port(s): 8097 (http) with context path '/zhida'
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,160][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,160][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,701][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76] - nacos registry, DEFAULT_GROUP SINITEK-NOCODE-BACKEND **************:8097 register finished
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,710][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:93] - Documentation plugins bootstrapped
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,713][springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79] - Found 2 custom documentation plugin(s)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,761][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,835][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: findOpenApiListUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:32,928][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,157][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,163][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,166][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,216][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,247][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,251][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,252][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,255][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,257][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,322][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,419][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,422][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,453][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,481][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: platformParamUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,482][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: loginUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,499][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageAuthUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,504][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getBaseSettingUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,505][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getInfoByCustomUrlUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,607][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,608][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveGroupUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,610][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageSceneUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,611][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,612][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,614][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deletePageAuthUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,615][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveBaseSettingUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,623][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageSceneUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,624][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,631][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: copyPageAuthUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,641][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageAuthUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,649][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,649][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFieldAuthDataUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,650][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFirstFormCodeUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,651][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getNameByCodeUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,651][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getUrlByCodeUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,652][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: sceneTypeListUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,653][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupListUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,808][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupTreeUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,809][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,809][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: moveUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,810][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getAllFormUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,815][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchPageAuthListUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,823][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getBaseInfoUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,824][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewSecretUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,826][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: settingUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,827][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_4
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,832][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,833][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,834][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateStatusUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,834][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: createUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,835][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,849][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,850][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: accessTokenUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,851][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getEnumerateUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,853][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getManageFormButtonsUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,855][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: applyHistoryVersionUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,855][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormPageDataUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,859][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPublishedFormUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,861][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateVersionUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,861][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_2
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,862][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_4
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,870][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getIdleTableLengthUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,872][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: effectVersionUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,872][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: publishUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,873][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveFiledConfigUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,874][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormDataManageSettingUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,879][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: versionHistoryUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,881][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateHistoryUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,885][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFieldMappingCodeUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,888][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getTemporaryUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,891][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,892][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: pageFormDataFillReportUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,896][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_5
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,897][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,898][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_5
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,900][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: temporarySaveUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,901][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,909][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_3
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,910][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: exportUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,917][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: agentGenerateFunctionUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,922][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingGET_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,923][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingHEAD_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,923][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPOST_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,924][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPUT_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,925][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPATCH_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,925][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingDELETE_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,931][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingOPTIONS_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,931][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingTRACE_1
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,940][org.springframework.boot.StartupInfoLogger.logStarted:61] - Started SirmApplication in 23.788 seconds (JVM running for 26.195)
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,954][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs
----------------------------------------------------------
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,957][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs?group=零代码平台
----------------------------------------------------------
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,961][com.sinitek.data.mybatis.init.EntityNameApplicationRunner.run:68] - 全局实体EntityName初始化完成,总匹配实体数量为: 0,自动设置实体数量为: 0, 请不要在当前代码执行前使用实体.ENTITY_NAME,启动之前可以使用 new 实体名().getEntityNameValue()方式获取到EntityName
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,962][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 对称加密 算法为: AES, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.symmetry.impl.AesSymmetryEncryptionImpl,该类是框架默认实现
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,962][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 非对称加密 算法为: RSA, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.asymmetric.impl.RsaAsymmetricEncryptionImpl,该类是框架默认实现
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,963][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 散列 算法为: MD5, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.hash.impl.MD5HashEncryptionImpl,该类是框架默认实现
[INFO][SIRM][92333,1,main][2025-06-20 16:55:33,990][com.sinitek.sirm.common.init.SiniCubeUtilsRunner.run:22] - 向ConvertUtils注册DateTimeConverter完成
[INFO][SIRM][92333,1,main][2025-06-20 16:55:34,014][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend-local.yml, group=DEFAULT_GROUP
[INFO][SIRM][92333,1,main][2025-06-20 16:55:34,015][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend.yml, group=DEFAULT_GROUP
[INFO][SIRM][92333,1,main][2025-06-20 16:55:34,015][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend, group=DEFAULT_GROUP
[INFO][SIRM][92333,1,main][2025-06-20 16:55:34,055][com.sinitek.cloud.common.cache.IPCache.getLocalIp:56] - 当前服务器的IPv4地址为: ***********
[INFO][SIRM][92333,112,temp-dir-cleaner-thread][2025-06-20 16:55:34,070][com.sinitek.sirm.common.tempdir.support.TempDirCleaner.run:55] - 自动清理临时目录功能启用状态：false
[INFO][SIRM][92333,1,main][2025-06-20 16:55:34,077][com.sinitek.sirm.nocode.utils.ApplicationStartUtil.startBackendApplication:29] - 
----------------------------------------------------------
	 http://***********:8097/zhida 运行成功
----------------------------------------------------------
[INFO][SIRM][92333,109,SinicubeThreadExecutor-1][2025-06-20 16:55:34,236][com.sinitek.data.model.recheck.support.RecheckSupport.createInstance:39] - 成功初始化复核模型，加载了0个复核实体定义
[INFO][SIRM][92333,81,http-nio-8097-exec-1][2025-06-20 16:59:06,208][org.springframework.web.servlet.FrameworkServlet.initServletBean:525] - Initializing Servlet 'dispatcherServlet'
[INFO][SIRM][92333,81,http-nio-8097-exec-1][2025-06-20 16:59:06,209][org.springframework.web.servlet.FrameworkServlet.initServletBean:547] - Completed initialization in 1 ms
[ERROR][SIRM][92333,82,http-nio-8097-exec-2][2025-06-20 16:59:06,526][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: 999000001, 接口/zhida/frontend/api/nocode/page/get-name发生异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/page/mapper/ZdPageMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.page.mapper.ZdPageMapper.selectOne
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at jdk.proxy2.$Proxy135.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:159) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:90) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.proxy2.$Proxy179.selectOne(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:201) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:229) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.dao.ZdPageDAO$$EnhancerBySpringCGLIB$$665b5120.getOne(<generated>) ~[sinitek-nocode-dal-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl.getNameByCode(ZdPageServiceImpl.java:127) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl$$FastClassBySpringCGLIB$$708a50d1.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl$$EnhancerBySpringCGLIB$$6dbfeaba.getNameByCode(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController.getNameByCode(ZdAppPageController.java:142) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController$$FastClassBySpringCGLIB$$2abfe69a.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController$$EnhancerBySpringCGLIB$$6695ceab.getNameByCode(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:1575) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/page/mapper/ZdPageMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.page.mapper.ZdPageMapper.selectOne
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:388) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:364) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:312) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:183) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:177) ~[?:?]
	at org.apache.ibatis.reflection.invoker.GetFieldInvoker.invoke(GetFieldInvoker.java:38) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:164) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.metaObjectForProperty(MetaObject.java:145) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:115) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.toolkit.PluginUtils.realTarget(PluginUtils.java:50) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:39) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy277.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy276.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
[ERROR][SIRM][92333,84,http-nio-8097-exec-4][2025-06-20 16:59:06,534][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: 999000001, 接口/zhida/frontend/api/nocode/form/view发生异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/form/mapper/ZdPageFormMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.form.mapper.ZdPageFormMapper.selectOne
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at jdk.proxy2.$Proxy135.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:159) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:90) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.proxy2.$Proxy191.selectOne(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:201) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:229) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.form.dao.ZdPageFormDAO$$EnhancerBySpringCGLIB$$5338cf3.getOne(<generated>) ~[sinitek-nocode-dal-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.service.impl.ZdPageFormServiceImpl.view(ZdPageFormServiceImpl.java:149) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.service.impl.ZdPageFormServiceImpl$$FastClassBySpringCGLIB$$7b473d18.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.form.service.impl.ZdPageFormServiceImpl$$EnhancerBySpringCGLIB$$f58d13ef.view(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.controller.ZdFormController.view(ZdFormController.java:143) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.controller.ZdFormController$$FastClassBySpringCGLIB$$98946867.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.form.controller.ZdFormController$$EnhancerBySpringCGLIB$$7f85c32.view(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:1575) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/form/mapper/ZdPageFormMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.form.mapper.ZdPageFormMapper.selectOne
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:388) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:364) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:312) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:183) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:177) ~[?:?]
	at org.apache.ibatis.reflection.invoker.GetFieldInvoker.invoke(GetFieldInvoker.java:38) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:164) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.metaObjectForProperty(MetaObject.java:145) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:115) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.toolkit.PluginUtils.realTarget(PluginUtils.java:50) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:39) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy277.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy276.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
[ERROR][SIRM][92333,83,http-nio-8097-exec-3][2025-06-20 16:59:06,532][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: 999000001, 接口/zhida/frontend/api/nocode/page/group-list发生异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/page/mapper/ZdPageMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.page.mapper.ZdPageMapper.selectList
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at jdk.proxy2.$Proxy135.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.proxy2.$Proxy179.selectList(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:279) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.dao.ZdPageDAO$$EnhancerBySpringCGLIB$$665b5120.list(<generated>) ~[sinitek-nocode-dal-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl.groupList(ZdPageServiceImpl.java:338) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl$$FastClassBySpringCGLIB$$708a50d1.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl$$EnhancerBySpringCGLIB$$6dbfeaba.groupList(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController.groupList(ZdAppPageController.java:111) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController$$FastClassBySpringCGLIB$$2abfe69a.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController$$EnhancerBySpringCGLIB$$6695ceab.groupList(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:1575) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/page/mapper/ZdPageMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.page.mapper.ZdPageMapper.selectList
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:388) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:364) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:312) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:183) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:177) ~[?:?]
	at org.apache.ibatis.reflection.invoker.GetFieldInvoker.invoke(GetFieldInvoker.java:38) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:164) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.metaObjectForProperty(MetaObject.java:145) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:115) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.toolkit.PluginUtils.realTarget(PluginUtils.java:50) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:39) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy277.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy276.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
[ERROR][SIRM][92333,85,http-nio-8097-exec-5][2025-06-20 16:59:06,530][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: 999000001, 接口/zhida/frontend/api/nocode/app/setting发生异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.exists
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at jdk.proxy2.$Proxy135.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:159) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:90) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.proxy2.$Proxy145.exists(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.BaseDAO.exists(BaseDAO.java:82) ~[sinitek-nocode-dal-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.app.service.impl.ZdAppServiceImpl.exists(ZdAppServiceImpl.java:142) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.app.service.impl.ZdAppServiceImpl$$FastClassBySpringCGLIB$$94934591.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.app.service.impl.ZdAppServiceImpl$$EnhancerBySpringCGLIB$$91b9c0fa.exists(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.appmanager.aspect.impl.ZdAppRightAspect.setDataRight(ZdAppRightAspect.java:61) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:617) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.aspectj.AspectJMethodBeforeAdvice.before(AspectJMethodBeforeAdvice.java:44) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:57) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.app.controller.ZdApplicationController$$EnhancerBySpringCGLIB$$72382953.setting(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:1575) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.exists
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 82 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:388) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:364) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:312) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:183) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:177) ~[?:?]
	at org.apache.ibatis.reflection.invoker.GetFieldInvoker.invoke(GetFieldInvoker.java:38) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:164) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.metaObjectForProperty(MetaObject.java:145) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:115) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.toolkit.PluginUtils.realTarget(PluginUtils.java:50) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:39) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy277.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy276.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 82 more
[ERROR][SIRM][92333,81,http-nio-8097-exec-1][2025-06-20 16:59:06,526][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: 999000001, 接口/zhida/frontend/api/nocode/page/group-tree发生异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/page/mapper/ZdPageMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.page.mapper.ZdPageMapper.selectList
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at jdk.proxy2.$Proxy135.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.proxy2.$Proxy179.selectList(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:279) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.dao.ZdPageDAO$$EnhancerBySpringCGLIB$$665b5120.list(<generated>) ~[sinitek-nocode-dal-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl.groupTree(ZdPageServiceImpl.java:347) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl$$FastClassBySpringCGLIB$$708a50d1.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.service.impl.ZdPageServiceImpl$$EnhancerBySpringCGLIB$$6dbfeaba.groupTree(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController.groupTree(ZdAppPageController.java:120) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController$$FastClassBySpringCGLIB$$2abfe69a.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController$$EnhancerBySpringCGLIB$$6695ceab.groupTree(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:1575) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/page/mapper/ZdPageMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.page.mapper.ZdPageMapper.selectList
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:388) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:364) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:312) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:183) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:177) ~[?:?]
	at org.apache.ibatis.reflection.invoker.GetFieldInvoker.invoke(GetFieldInvoker.java:38) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:164) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.metaObjectForProperty(MetaObject.java:145) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:115) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.toolkit.PluginUtils.realTarget(PluginUtils.java:50) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:39) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy277.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy276.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
[ERROR][SIRM][92333,86,http-nio-8097-exec-6][2025-06-20 16:59:06,529][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: 999000001, 接口/zhida/frontend/api/nocode/page/list发生异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.exists
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at jdk.proxy2.$Proxy135.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:159) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:90) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.proxy2.$Proxy145.exists(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.support.BaseDAO.exists(BaseDAO.java:82) ~[sinitek-nocode-dal-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.app.service.impl.ZdAppServiceImpl.exists(ZdAppServiceImpl.java:142) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.app.service.impl.ZdAppServiceImpl$$FastClassBySpringCGLIB$$94934591.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.app.service.impl.ZdAppServiceImpl$$EnhancerBySpringCGLIB$$91b9c0fa.exists(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.appmanager.aspect.impl.ZdAppRightAspect.setDataRight(ZdAppRightAspect.java:61) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:617) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.aspectj.AspectJMethodBeforeAdvice.before(AspectJMethodBeforeAdvice.java:44) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:57) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.page.controller.ZdAppPageController$$EnhancerBySpringCGLIB$$6695ceab.list(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:1575) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/common/mapper/CommonMapper.xml
### The error may involve com.sinitek.sirm.nocode.common.mapper.CommonMapper.exists
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 82 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:388) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:364) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:312) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:183) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:177) ~[?:?]
	at org.apache.ibatis.reflection.invoker.GetFieldInvoker.invoke(GetFieldInvoker.java:38) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:164) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.metaObjectForProperty(MetaObject.java:145) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:115) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.toolkit.PluginUtils.realTarget(PluginUtils.java:50) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:39) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy277.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy276.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 82 more
[ERROR][SIRM][92333,87,http-nio-8097-exec-7][2025-06-20 16:59:10,277][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: 999000001, 接口/zhida/frontend/api/nocode/form/view发生异常
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/form/mapper/ZdPageFormMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.form.mapper.ZdPageFormMapper.selectOne
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at jdk.proxy2.$Proxy135.selectOne(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:159) ~[mybatis-spring-2.0.5.jar:2.0.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:90) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.proxy2.$Proxy191.selectOne(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:201) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:229) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>) ~[mybatis-plus-extension-3.4.1.jar:3.4.1]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137) ~[spring-tx-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.form.dao.ZdPageFormDAO$$EnhancerBySpringCGLIB$$5338cf3.getOne(<generated>) ~[sinitek-nocode-dal-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.service.impl.ZdPageFormServiceImpl.view(ZdPageFormServiceImpl.java:149) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.service.impl.ZdPageFormServiceImpl$$FastClassBySpringCGLIB$$7b473d18.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.form.service.impl.ZdPageFormServiceImpl$$EnhancerBySpringCGLIB$$f58d13ef.view(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.controller.ZdFormController.view(ZdFormController.java:143) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at com.sinitek.sirm.nocode.form.controller.ZdFormController$$FastClassBySpringCGLIB$$98946867.invoke(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sinitek.sirm.nocode.form.controller.ZdFormController$$EnhancerBySpringCGLIB$$7f85c32.view(<generated>) ~[sinitek-nocode-service-design-1.0.0-SNAPSHOT.jar:1.0.0-SNAPSHOT]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:1575) ~[?:?]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
### The error may exist in com/sinitek/sirm/nocode/form/mapper/ZdPageFormMapper.java (best guess)
### The error may involve com.sinitek.sirm.nocode.form.mapper.ZdPageFormMapper.selectOne
### The error occurred while executing a query
### Cause: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
Caused by: java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module @6d2a209c
	at java.lang.reflect.AccessibleObject.throwInaccessibleObjectException(AccessibleObject.java:388) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:364) ~[?:?]
	at java.lang.reflect.AccessibleObject.checkCanSetAccessible(AccessibleObject.java:312) ~[?:?]
	at java.lang.reflect.Field.checkCanSetAccessible(Field.java:183) ~[?:?]
	at java.lang.reflect.Field.setAccessible(Field.java:177) ~[?:?]
	at org.apache.ibatis.reflection.invoker.GetFieldInvoker.invoke(GetFieldInvoker.java:38) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:164) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.metaObjectForProperty(MetaObject.java:145) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:115) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.toolkit.PluginUtils.realTarget(PluginUtils.java:50) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.sinitek.data.mybatis.plugins.CustomerFuncationInterceptor.intercept(CustomerFuncationInterceptor.java:39) ~[sinitek-data-mybatis-7.4.640.jar:7.4.640]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy277.prepare(Unknown Source) ~[?:?]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:94) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:68) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156) ~[mybatis-3.5.6.jar:3.5.6]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:92) ~[mybatis-plus-core-3.4.1.jar:3.4.1]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.proxy2.$Proxy276.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140) ~[mybatis-3.5.6.jar:3.5.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76) ~[mybatis-3.5.6.jar:3.5.6]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426) ~[mybatis-spring-2.0.5.jar:2.0.5]
	... 90 more
[WARN][SIRM][92333,36,Thread-1][2025-06-20 17:00:28,505][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:102] - [HttpClientBeanHolder] Start destroying common HttpClient
[WARN][SIRM][92333,36,Thread-1][2025-06-20 17:00:28,505][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:111] - [HttpClientBeanHolder] Destruction of the end
[WARN][SIRM][92333,45,Thread-7][2025-06-20 17:00:28,508][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:136] - [NotifyCenter] Start destroying Publisher
[WARN][SIRM][92333,45,Thread-7][2025-06-20 17:00:28,508][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:153] - [NotifyCenter] Destruction of the end
[INFO][SIRM][92333,61,SpringApplicationShutdownHook][2025-06-20 17:00:29,548][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:95] - De-registering from Nacos Server now...
[INFO][SIRM][92333,61,SpringApplicationShutdownHook][2025-06-20 17:00:29,563][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:115] - De-registration finished.
[INFO][SIRM][92333,61,SpringApplicationShutdownHook][2025-06-20 17:00:29,572][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][92333,61,SpringApplicationShutdownHook][2025-06-20 17:00:29,579][com.alibaba.druid.pool.DruidDataSource.close:2051] - {dataSource-1} closing ...
[INFO][SIRM][92333,61,SpringApplicationShutdownHook][2025-06-20 17:00:29,580][com.alibaba.druid.pool.DruidDataSource.close:2124] - {dataSource-1} closed
