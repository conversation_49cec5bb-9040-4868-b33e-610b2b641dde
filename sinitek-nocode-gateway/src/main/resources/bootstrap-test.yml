spring:
  cloud:
    nacos:
      discovery:
        #server-addr: 192.168.21.121:28848
        server-addr: ${NACOS-SERVER}
        namespace: ${NACOS-NAMESPACE}
        enabled: true
        username: ${NACOS-USERNAME}
        password: ${NACOS-PASSWORD}
      config:
        #server-addr: 192.168.21.121:28848
        server-addr: ${NACOS-SERVER}
        namespace: ${NACOS-NAMESPACE}
        username: ${NACOS-USERNAME}
        password: ${NACOS-PASSWORD}
        file-extension: yaml
sinicube:
  sirmapp:
    remote:
      service-name: CLOUD-SIRMAPP-74