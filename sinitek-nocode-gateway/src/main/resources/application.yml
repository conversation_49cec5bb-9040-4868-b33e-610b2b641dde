server:
  port: 5000

spring:
  profiles:
    active: dev
  application:
    name: cloud-gateway-7.4-test
  cloud: # spring cloud gateway 路由配置方式
    gateway:
      predicate:
        weight:
          enabled: false
      default-filters:
        - DedupeResponseHeader=Vary Access-Control-Allow-Origin
      discovery: #是否与服务发现组件进行结合，通过 serviceId(必须设置成大写) 转发到具体的服务实例。默认为false，设为true便开启通过服务中心的自动根据 serviceId 创建路由的功能。
        locator: #路由访问方式：http://Gateway_HOST:Gateway_PORT/大写的serviceId/**，其中微服务应用名默认大写访问。
          enabled: true
feign:
  hystrix:
    enabled: false


hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 80000

ribbon:
  ConnectTimeout: 30000
  ReadTimeout: 60000

sinicube:
  ## 主服务的服务名称
  sirmapp:
    remote:
      service-name: CLOUD-SIRMAPP-74
  validator:
    enable: false

  gateway:
    excludePathList:
    auth-exclude-path-list:
  csrf:
    ## 是否启用跨站点请求验证,默认为false
    enable: false

    ## 信任的白名单列表
    whiteList:
      - **************:8081
      - 127.0.0.1:5000
  cors:
  ## 设置Access-Control-Allow-Origin
#    origins:
#      - http://localhost:5000
#      - http://127.0.0.1:5000
