spring:
  cloud:
    nacos:
      ## 不用配置中心的情况下,该配置无用
      config:
        server-addr: 192.168.148.134:8848
        enabled: true
        file-extension: yaml
        namespace: bb40ff3d-9ad4-431e-9275-0cf43ddee4cb
        username: nacos
        password: nacos
      discovery:
        server-addr: 192.168.148.134:8848
        namespace: bb40ff3d-9ad4-431e-9275-0cf43ddee4cb
        username: nacos
        password: nacos