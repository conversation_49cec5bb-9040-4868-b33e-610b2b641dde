package com.sinitek.sirm.nocode.form.support.datascope;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.support.datascope.base.DataScopeConditionSetter;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 本人提交的数据权限
 *
 * <AUTHOR>
 * @version 2025.0325
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class SelfDataScope implements DataScopeConditionSetter {
    @Override
    public void setCondition(QueryWrapper<ZdPageFormData> queryWrapper, ZdPageAuthDTO auth, String currentOrgId, Set<String> orgIds) {
        // 本人提交的数据
        orgIds.add(currentOrgId);

    }
}
