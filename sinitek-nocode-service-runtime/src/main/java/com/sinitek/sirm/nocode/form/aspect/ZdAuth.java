package com.sinitek.sirm.nocode.form.aspect;

import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限
 *
 * <AUTHOR>
 * @version 2025.0328
 * @since 1.0.0-SNAPSHOT
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ZdAuth {
    /**
     * 操作权限类型,默认查看
     *
     * @return 操作权限类型
     */
    OperationAuthEnum operationAuthType() default OperationAuthEnum.VIEW;

    /**
     * 是否检查应用状态
     *
     * @return 是否检查应用状态
     */
    boolean checkStatus() default false;

}
