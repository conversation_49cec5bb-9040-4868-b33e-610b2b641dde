package com.sinitek.sirm.nocode.form.support.datascope.base;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;

import java.util.Set;

/**
 * 数据范围设置器
 *
 * <AUTHOR>
 * @version 2025.0325
 * @since 1.0.0-SNAPSHOT
 */
public interface DataScopeConditionSetter {
    /**
     * 设置数据范围条件
     *
     * @param queryWrapper 查询条件
     * @param auth         数据权限
     * @param currentOrgId 当前用户所属组织
     * @param orgIds       当前用户所属组织及子组织
     */
    void setCondition(QueryWrapper<ZdPageFormData> queryWrapper, ZdPageAuthDTO auth, String currentOrgId, Set<String> orgIds);
}
