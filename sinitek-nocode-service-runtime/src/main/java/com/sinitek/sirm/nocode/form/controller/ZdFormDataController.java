package com.sinitek.sirm.nocode.form.controller;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import com.sinitek.sirm.nocode.form.aspect.ZdAuth;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataBatchModelDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataExportDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataModelDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDataFillReportDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataService;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0407
 * @description 表单数据管理
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/form/data", tags = "表单数据管理")
@RequestMapping("/frontend/api/nocode/form/data")
public class ZdFormDataController {

    @Resource
    private IZdPageFormDataService pageFormDataService;


    @ApiOperation(value = "查询数据列表")
    @PostMapping("/search")
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    public TableResult<ZdPageFormDataDTO> search(@RequestBody @ApiParam(value = "表单数据查询条件", name = "表单数据查询条件") @Valid ZdFormDataSearchParamDTO param) {
        param.setCurrentOrgId(CurrentUserFactory.getOrgId());
        return pageFormDataService.search(param);
    }

    @ApiOperation(value = "查看某条数据详情")
    @GetMapping("/view")
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    public RequestResult<ZdPageFormDataDTO> view(
            @RequestParam(FormConstant.FORM_CODE)
            @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
            String formCode,
            @RequestParam("id")
            @ApiParam(value = "数据主键", name = "id")
            Long id
    ) {
        return new RequestResult<>(pageFormDataService.view(formCode, id));
    }


    @ApiOperation(value = "获取多条数据")
    @GetMapping("/list")
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    public RequestResult<List<ZdPageFormDataDTO>> list(@RequestParam(FormConstant.FORM_CODE)
                                                       @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
                                                       String formCode,
                                                       @RequestBody
                                                       @ApiParam(value = "数据主键集合", name = "idList")
                                                       List<Long> idList
    ) {
        return new RequestResult<>(pageFormDataService.list(formCode, idList));
    }


    @ZdAuth(operationAuthType = OperationAuthEnum.DELETE, checkStatus = true)
    @ApiOperation(value = "删除一条数据")
    @PostMapping("/delete")
    public RequestResult<Boolean> delete(@RequestBody @ApiParam(value = "表单数据查询条件", name = "表单数据查询条件") @Validated(value = ZdIdDTO.Update.class) ZdFormDataModelDTO idDTO) {
        return new RequestResult<>(pageFormDataService.delete(idDTO));
    }

    /**
     * 批量删除数据
     *
     * @param idDTO 参数
     * @return 是否成功
     */

    @ZdAuth(operationAuthType = OperationAuthEnum.DELETE, checkStatus = true)
    @ApiOperation(value = "批量删除数据")
    @PostMapping("/delete-batch")
    public RequestResult<Boolean> deleteBatch(@RequestBody @ApiParam(value = "表单数据查询条件", name = "表单数据查询条件") @Validated(value = ZdIdDTO.Delete.class) ZdFormDataBatchModelDTO idDTO) {
        return new RequestResult<>(pageFormDataService.deleteBatch(idDTO));
    }


    @ZdAuth(operationAuthType = OperationAuthEnum.SUBMIT, checkStatus = true)
    @ApiOperation(value = "修改或者是修改表单数据")
    @PostMapping("/save-or-update")
    public RequestResult<Long> saveOrUpdate(@RequestBody @ApiParam(value = "表单数据保存或者修改", name = "表单数据保存或者修改") @Valid ZdPageFormDataDTO zdPageFormDataDTO) {
        zdPageFormDataDTO.setCreatorId(CurrentUserFactory.getUserId());
        return new RequestResult<>(pageFormDataService.saveOrUpdate(zdPageFormDataDTO));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.TEMPORARY_SAVE, checkStatus = true)
    @ApiOperation(value = "表单数据暂存")
    @PostMapping("/temporary-save")
    public RequestResult<Long> temporarySave(@RequestBody @ApiParam(value = "表单数据暂存", name = "表单数据暂存") @Valid ZdPageFormDataDTO zdPageFormDataDTO) {
        zdPageFormDataDTO.setCreatorId(CurrentUserFactory.getUserId());
        return new RequestResult<>(pageFormDataService.temporarySave(zdPageFormDataDTO));
    }

    @ZdAuth(operationAuthType = OperationAuthEnum.TEMPORARY_SAVE)
    @ApiOperation(value = "获取当前登录人表单暂存数据")
    @GetMapping("/get-temporary")
    public RequestResult<ZdPageFormDataDTO> getTemporary(
            @RequestParam(FormConstant.FORM_CODE)
            @ApiParam(value = "表单编码", name = FormConstant.FORM_CODE)
            String formCode
    ) {
        return new RequestResult<>(pageFormDataService.getTemporary(formCode, CurrentUserFactory.getUserId()));
    }


    @ApiOperation(value = "收集表场景结果报告")
    @GetMapping("/page-form-data-fill-report")
    public RequestResult<ZdPageFormDataFillReportDTO> pageFormDataFillReport(
            @ApiParam(name = FormConstant.FORM_CODE, value = "表单编码")
            @RequestParam(FormConstant.FORM_CODE) String formCode,
            @ApiParam(name = FormConstant.REPORT_PARAM, value = "报告参数，在地址上可以获取到")
            @RequestParam(FormConstant.REPORT_PARAM) String reportParam
    ) {
        return new RequestResult<>(pageFormDataService.pageFormDataFillReport(formCode, reportParam));
    }

    /**
     * 导出数据,既然有查看数据的权限，那么也应该有导出数据的权限
     * todo 是否需要异步执行
     *
     * @param zdFormDataExportDTO 参数
     */
    @ZdAuth(operationAuthType = OperationAuthEnum.VIEW)
    @ApiOperation(value = "表单数据导出")
    @PostMapping("/export")
    public void export(@RequestBody @ApiParam(value = "表单数据导出", name = "表单数据导出") @Validated(value = ZdIdDTO.View.class) ZdFormDataExportDTO zdFormDataExportDTO) {

    }
}
