package com.sinitek.sirm.nocode.form.support.datascope;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.support.datascope.base.DataScopeConditionSetter;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.enumerate.DepartmentEmployeeQueryScopeEnum;
import com.sinitek.sirm.org.service.IOrgService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 同级部门数据权限
 *
 * <AUTHOR>
 * @version 2025.0325
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class SameDepartmentDataScope implements DataScopeConditionSetter {
    @Resource
    private IOrgService orgService;

    @Override
    public void setCondition(QueryWrapper<ZdPageFormData> queryWrapper, ZdPageAuthDTO auth, String currentOrgId, Set<String> orgIds) {
        // 同级部门
        List<Employee> employeeList = orgService.findDepartmentEmployeesByEmpId(currentOrgId, DepartmentEmployeeQueryScopeEnum.ALL);
        if (CollectionUtils.isNotEmpty(employeeList)) {
            orgIds.addAll(employeeList.stream().map(Employee::getId).collect(Collectors.toList()));
        }
    }
}
