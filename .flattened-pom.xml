<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sinitek.sinicube</groupId>
    <artifactId>sinitek-sinicube</artifactId>
    <version>7.4.640</version>
  </parent>
  <groupId>com.sinitek.sirm</groupId>
  <artifactId>sinitek-nocode</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>sinitek-nocode-api</module>
    <module>sinitek-nocode-service-design</module>
    <module>sinitek-nocode-gateway</module>
    <module>sinitek-nocode-service-runtime</module>
    <module>sinitek-nocode-dal</module>
    <module>sinitek-nocode-assembly</module>
    <module>sinitek-nocode-sdk</module>
    <module>sinitek-nocode-service-llm</module>
  </modules>
  <properties>
    <skipTests>true</skipTests>
    <postgresql.version>42.3.1</postgresql.version>
    <revision>1.0.0-SNAPSHOT</revision>
    <java.version>1.8</java.version>
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>${postgresql.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sinitek.sirm</groupId>
        <artifactId>sinitek-nocode-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sinitek.sirm</groupId>
        <artifactId>sinitek-nocode-dal</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sinitek.sirm</groupId>
        <artifactId>sinitek-nocode-service-design</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sinitek.sirm</groupId>
        <artifactId>sinitek-nocode-service-runtime</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sinitek.sirm</groupId>
        <artifactId>sinitek-nocode-service-llm</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.spockframework</groupId>
        <artifactId>spock-bom</artifactId>
        <version>2.0-groovy-3.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <targetPath>..</targetPath>
        <filtering>true</filtering>
        <directory>src/main/docker</directory>
        <includes>
          <include>**/*.*</include>
          <include>**/*</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/java</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <artifactId>sonar-maven-plugin</artifactId>
        <version>3.7.0.1746</version>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>default-cli</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <fail>true</fail>
              <rules>
                <banDuplicateClasses />
                <dependencyConvergence />
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.2.7</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
