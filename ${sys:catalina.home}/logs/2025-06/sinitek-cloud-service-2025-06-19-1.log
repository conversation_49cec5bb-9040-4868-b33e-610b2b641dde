[WARN][SIRM][16717,28,Thread-7][2025-06-19 09:49:19,144][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:102] - [HttpClientBeanHolder] Start destroying common HttpClient
[WARN][SIRM][16717,38,Thread-14][2025-06-19 09:49:19,144][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:136] - [NotifyCenter] Start destroying Publisher
[WARN][SIRM][16717,38,Thread-14][2025-06-19 09:49:19,231][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:153] - [NotifyCenter] Destruction of the end
[WARN][SIRM][16717,28,Thread-7][2025-06-19 09:49:19,232][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:111] - [HttpClientBeanHolder] Destruction of the end
[INFO][SIRM][16717,58,SpringApplicationShutdownHook][2025-06-19 09:49:20,327][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:95] - De-registering from Nacos Server now...
[INFO][SIRM][16717,58,SpringApplicationShutdownHook][2025-06-19 09:49:20,394][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:115] - De-registration finished.
[INFO][SIRM][16717,58,SpringApplicationShutdownHook][2025-06-19 09:49:20,439][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][16717,58,SpringApplicationShutdownHook][2025-06-19 09:49:20,490][com.alibaba.druid.pool.DruidDataSource.close:2051] - {dataSource-1} closing ...
[INFO][SIRM][16717,58,SpringApplicationShutdownHook][2025-06-19 09:49:20,497][com.alibaba.druid.pool.DruidDataSource.close:2124] - {dataSource-1} closed
[INFO][SIRM][72759,1,main][2025-06-19 09:49:36,569][com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[INFO][SIRM][72759,25,background-preinit][2025-06-19 09:49:36,584][org.hibernate.validator.internal.util.Version.<clinit>:21] - HV000001: Hibernate Validator 6.2.5.Final
[INFO][SIRM][72759,1,main][2025-06-19 09:49:37,802][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][72759,1,main][2025-06-19 09:49:37,803][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[WARN][SIRM][72759,1,main][2025-06-19 09:49:39,702][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend] & group[DEFAULT_GROUP]
[WARN][SIRM][72759,1,main][2025-06-19 09:49:39,802][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend-local.yml] & group[DEFAULT_GROUP]
[INFO][SIRM][72759,1,main][2025-06-19 09:49:39,804][org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.doInitialize:134] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP'}]
[INFO][SIRM][72759,1,main][2025-06-19 09:49:39,912][org.springframework.boot.SpringApplication.logStartupProfileInfo:651] - The following 1 profile is active: "local"
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,046][org.springframework.cloud.context.scope.GenericScope.setSerializationId:283] - BeanFactory id=6c014e2d-6920-30c2-ab78-d92147d26a10
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,243][com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39] - Post-processing PropertySource instances
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,247][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,248][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,249][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,390][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,390][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,391][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,391][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,391][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,391][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,392][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,392][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,392][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,392][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap-local.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,392][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,393][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,453][com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2:31] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,517][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'hibernateValidateConfig' of type [com.sinitek.sirm.config.HibernateValidateConfig$$EnhancerBySpringCGLIB$$f63eaf25] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,521][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'springMessageConfig' of type [com.sinitek.sirm.config.SpringMessageConfig$$EnhancerBySpringCGLIB$$deef5f17] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,537][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,543][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,545][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$501/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,550][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,557][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,558][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,594][com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2:35] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,596][com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2:35] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,656][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'i18nProperties' of type [com.sinitek.sirm.i18n.properties.I18nProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,694][com.sinitek.sirm.config.SpringMessageConfig.messageSource:52] - 加载默认消息资源文件：classpath*:message/messages-*.properties
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,714][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'messageSource' of type [org.springframework.context.support.ReloadableResourceBundleMessageSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:44,760][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'validator' of type [org.springframework.validation.beanvalidation.LocalValidatorFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:45,212][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'cacheConfig' of type [com.sinitek.sirm.config.CacheConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:46,030][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108] - Tomcat initialized with port(s): 8097 (http)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:46,286][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:290] - Root WebApplicationContext: initialization completed in 6283 ms
[INFO][SIRM][72759,1,main][2025-06-19 09:49:47,357][com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55] - Init DruidDataSource
[INFO][SIRM][72759,1,main][2025-06-19 09:49:49,108][com.alibaba.druid.pool.DruidDataSource.init:972] - {dataSource-1} inited
[INFO][SIRM][72759,1,main][2025-06-19 09:49:52,633][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.<init>:38] - flyway jdbcUrl: **********************************************************?useUnicode=true&characterEncoding=utf8&currentSchema=public&stringtype=unspecified
[INFO][SIRM][72759,1,main][2025-06-19 09:49:53,279][com.sinitek.sirm.ip.limit.interceptor.IpWhiteListInterceptor.init:56] - 当前ip限制状态为: false, true为开启状态,false为关闭状态
[INFO][SIRM][72759,1,main][2025-06-19 09:49:53,918][com.sinitek.sirm.config.SpringMessageConfig.localeChangeInterceptor:123] - 国际化开启状态: false, 系统默认的语言环境为: zh_CN,当前系统支持的语言环境列表: [LocaleInfo(locale=zh_CN, localeName=简体中文, localeChineseName=简体中文), LocaleInfo(locale=zh_HK, localeName=繁體中文, localeChineseName=繁体中文)]
[INFO][SIRM][72759,1,main][2025-06-19 09:49:54,193][springfox.documentation.spring.web.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[INFO][SIRM][72759,1,main][2025-06-19 09:49:54,333][com.sinitek.cloud.base.config.CloudSiniCubeConfig.currentUserInfo:37] - 当前com.sinitek.sirm.common.user.bridging.ICurrentUserInfo的实现类为: com.sinitek.cloud.base.user.CloudServerCurrentUserInfoImpl
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,247][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:46] - 开始检查有没有执行错误的脚本，如果有则先清理掉
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,273][com.zaxxer.hikari.HikariDataSource.getConnection:110] - HikariPool-1 - Starting...
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,405][com.zaxxer.hikari.HikariDataSource.getConnection:123] - HikariPool-1 - Start completed.
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,439][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,440][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Database: ********************************************************** (PostgreSQL 13.2)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,655][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Repair of failed migration in Schema History table "public"."flyway_schema_history" not necessary. No failed migration detected.
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,739][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Successfully repaired schema history table "public"."flyway_schema_history" (execution time 00:00.183s).
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,814][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:48] - 开始执行SQL脚本
[INFO][SIRM][72759,1,main][2025-06-19 09:49:56,842][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][72759,1,main][2025-06-19 09:49:57,012][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Current version of schema "public": 100.20250605.01
[INFO][SIRM][72759,1,main][2025-06-19 09:49:57,028][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Schema "public" is up to date. No migration necessary.
[INFO][SIRM][72759,1,main][2025-06-19 09:49:57,968][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][72759,1,main][2025-06-19 09:49:57,968][com.zaxxer.hikari.HikariDataSource.close:350] - HikariPool-1 - Shutdown initiated...
[INFO][SIRM][72759,1,main][2025-06-19 09:49:57,985][com.zaxxer.hikari.HikariDataSource.close:352] - HikariPool-1 - Shutdown completed.
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,232][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220] - Tomcat started on port(s): 8097 (http) with context path '/zhida'
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,255][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,256][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,628][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76] - nacos registry, DEFAULT_GROUP SINITEK-NOCODE-BACKEND **************:8097 register finished
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,637][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:93] - Documentation plugins bootstrapped
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,645][springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79] - Found 2 custom documentation plugin(s)
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,700][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][72759,1,main][2025-06-19 09:49:58,936][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: findOpenApiListUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,172][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,510][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,526][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,538][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,656][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,778][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,799][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_2
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,808][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,821][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_2
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,841][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:49:59,873][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,072][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,093][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_2
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,099][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,116][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: platformParamUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,119][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: loginUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,144][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageAuthUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,149][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getBaseSettingUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,157][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getInfoByCustomUrlUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,164][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,165][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveGroupUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,176][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageSceneUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,198][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,210][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_2
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,220][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveBaseSettingUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,233][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageSceneUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,241][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_2
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,245][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: copyPageAuthUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,257][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageAuthUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,262][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_2
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,267][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFirstFormCodeUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,270][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getNameByCodeUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,273][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getUrlByCodeUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,282][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: sceneTypeListUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,285][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupListUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,297][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupTreeUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,298][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_2
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,301][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: moveUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,310][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getAllFormUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,320][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchPageAuthListUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,326][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewSecretUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,334][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: settingUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,335][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_4
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,335][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getStatusUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,339][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,343][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,346][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateStatusUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,351][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: createUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,357][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,362][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_2
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,371][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: accessTokenUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,383][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getEnumerateUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,391][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getManageFormButtonsUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,402][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: applyHistoryVersionUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,404][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormPageDataUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,405][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPublishedFormUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,407][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateVersionUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,410][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_2
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,416][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_4
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,419][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getIdleTableLengthUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,434][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFieldMappingCodeUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,439][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: effectVersionUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,440][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: publishUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,449][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveFiledConfigUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,459][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormDataManageSettingUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,463][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: versionHistoryUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,472][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateHistoryUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,485][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getTemporaryUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,488][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,490][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: pageFormDataFillReportUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,492][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_5
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,498][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,503][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_5
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,505][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: temporarySaveUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,508][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,527][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_3
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,532][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: exportUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,535][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: agentGenerateFunctionUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,542][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingGET_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,547][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingHEAD_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,549][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPOST_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,552][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPUT_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,559][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPATCH_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,564][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingDELETE_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,568][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingOPTIONS_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,571][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingTRACE_1
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,593][org.springframework.boot.StartupInfoLogger.logStarted:61] - Started SirmApplication in 25.366 seconds (JVM running for 29.281)
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,608][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs
----------------------------------------------------------
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,608][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs?group=零代码平台
----------------------------------------------------------
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,613][com.sinitek.data.mybatis.init.EntityNameApplicationRunner.run:68] - 全局实体EntityName初始化完成,总匹配实体数量为: 0,自动设置实体数量为: 0, 请不要在当前代码执行前使用实体.ENTITY_NAME,启动之前可以使用 new 实体名().getEntityNameValue()方式获取到EntityName
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,614][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 对称加密 算法为: AES, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.symmetry.impl.AesSymmetryEncryptionImpl,该类是框架默认实现
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,614][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 非对称加密 算法为: RSA, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.asymmetric.impl.RsaAsymmetricEncryptionImpl,该类是框架默认实现
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,615][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 散列 算法为: MD5, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.hash.impl.MD5HashEncryptionImpl,该类是框架默认实现
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,640][com.sinitek.sirm.common.init.SiniCubeUtilsRunner.run:22] - 向ConvertUtils注册DateTimeConverter完成
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,678][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend-local.yml, group=DEFAULT_GROUP
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,684][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend.yml, group=DEFAULT_GROUP
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,685][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend, group=DEFAULT_GROUP
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,768][com.sinitek.cloud.common.cache.IPCache.getLocalIp:56] - 当前服务器的IPv4地址为: ***********
[INFO][SIRM][72759,152,temp-dir-cleaner-thread][2025-06-19 09:50:00,825][com.sinitek.sirm.common.tempdir.support.TempDirCleaner.run:55] - 自动清理临时目录功能启用状态：false
[INFO][SIRM][72759,1,main][2025-06-19 09:50:00,875][com.sinitek.sirm.nocode.utils.ApplicationStartUtil.startBackendApplication:29] - 
----------------------------------------------------------
	 http://***********:8097/zhida 运行成功
----------------------------------------------------------
[INFO][SIRM][72759,149,SinicubeThreadExecutor-1][2025-06-19 09:50:01,529][com.sinitek.data.model.recheck.support.RecheckSupport.createInstance:39] - 成功初始化复核模型，加载了0个复核实体定义
[INFO][SIRM][72759,115,http-nio-8097-exec-1][2025-06-19 13:58:41,724][org.springframework.web.servlet.FrameworkServlet.initServletBean:525] - Initializing Servlet 'dispatcherServlet'
[INFO][SIRM][72759,115,http-nio-8097-exec-1][2025-06-19 13:58:41,791][org.springframework.web.servlet.FrameworkServlet.initServletBean:547] - Completed initialization in 57 ms
[ERROR][SIRM][72759,115,http-nio-8097-exec-1][2025-06-19 13:58:42,601][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/app/search发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy148.current(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy148.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
[ERROR][SIRM][72759,116,http-nio-8097-exec-2][2025-06-19 13:58:48,790][com.sinitek.sirm.framework.support.FrontendResponseSupport.logError:240] - 客户端IP: 127.0.0.1, 当前用户orgId: , 接口/zhida/frontend/api/nocode/app/search发生异常
feign.FeignException$Unauthorized: [401] during [GET] to [http://**************:10001/frontend/api/org/currentuser] [IRemoteOrgUserService#current(String)]: [{"message":"010110","resultcode":"010110","data":null,"success":false}]
	at feign.FeignException.clientErrorStatus(FeignException.java:215) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:194) ~[feign-core-11.10.jar:?]
	at feign.FeignException.errorStatus(FeignException.java:185) ~[feign-core-11.10.jar:?]
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92) ~[feign-core-11.10.jar:?]
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:98) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:141) ~[feign-core-11.10.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91) ~[feign-core-11.10.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100) ~[feign-core-11.10.jar:?]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53) ~[spring-cloud-openfeign-core-3.1.9.jar:3.1.9]
	at com.sun.proxy.$Proxy271.current(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy148.current(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344) ~[spring-aop-5.3.39.jar:5.3.39]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234) ~[spring-aop-5.3.39.jar:5.3.39]
	at com.sun.proxy.$Proxy148.current(Unknown Source) ~[?:?]
	at com.sinitek.sirm.nocode.common.config.TokenCreateInterceptor.preHandle(TokenCreateInterceptor.java:86) ~[classes/:?]
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:148) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) ~[spring-webmvc-5.3.39.jar:5.3.39]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) ~[tomcat-embed-core-9.0.105.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) ~[tomcat-embed-websocket-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87) ~[knife4j-spring-2.0.8.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114) ~[druid-1.2.11.jar:1.2.11]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) ~[tomcat-embed-core-9.0.105.jar:9.0.105]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_452]
