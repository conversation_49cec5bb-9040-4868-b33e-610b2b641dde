[INFO][SIRM][9389,1,main][2025-06-24 08:49:25,656][com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[INFO][SIRM][9389,18,background-preinit][2025-06-24 08:49:25,681][org.hibernate.validator.internal.util.Version.<clinit>:21] - HV000001: Hibernate Validator 6.2.5.Final
[INFO][SIRM][9389,1,main][2025-06-24 08:49:27,731][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][9389,1,main][2025-06-24 08:49:27,733][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[WARN][SIRM][9389,1,main][2025-06-24 08:49:30,695][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend] & group[DEFAULT_GROUP]
[WARN][SIRM][9389,1,main][2025-06-24 08:49:30,824][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend-local.yml] & group[DEFAULT_GROUP]
[INFO][SIRM][9389,1,main][2025-06-24 08:49:30,828][org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.doInitialize:134] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP'}]
[INFO][SIRM][9389,1,main][2025-06-24 08:49:30,999][org.springframework.boot.SpringApplication.logStartupProfileInfo:651] - The following 1 profile is active: "local"
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,133][org.springframework.cloud.context.scope.GenericScope.setSerializationId:283] - BeanFactory id=bcd20513-c4ed-332b-9709-cdff1cb9c6d2
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,700][com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39] - Post-processing PropertySource instances
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,707][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,708][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,708][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,798][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,800][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,800][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,801][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,802][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,802][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,802][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,802][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,803][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,803][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap-local.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,803][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,803][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][9389,1,main][2025-06-24 08:49:37,850][com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2:31] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,081][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'hibernateValidateConfig' of type [com.sinitek.sirm.config.HibernateValidateConfig$$EnhancerBySpringCGLIB$$256ac593] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,093][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'springMessageConfig' of type [com.sinitek.sirm.config.SpringMessageConfig$$EnhancerBySpringCGLIB$$e1b7585] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,124][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,130][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,132][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$494/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,142][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,158][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,160][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,237][com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2:35] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,242][com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2:35] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,330][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'i18nProperties' of type [com.sinitek.sirm.i18n.properties.I18nProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,372][com.sinitek.sirm.config.SpringMessageConfig.messageSource:52] - 加载默认消息资源文件：classpath*:message/messages-*.properties
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,403][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'messageSource' of type [org.springframework.context.support.ReloadableResourceBundleMessageSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:38,474][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'validator' of type [org.springframework.validation.beanvalidation.LocalValidatorFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:40,027][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'cacheConfig' of type [com.sinitek.sirm.config.CacheConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:42,085][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108] - Tomcat initialized with port(s): 8097 (http)
[INFO][SIRM][9389,1,main][2025-06-24 08:49:42,635][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:290] - Root WebApplicationContext: initialization completed in 11542 ms
[INFO][SIRM][9389,1,main][2025-06-24 08:49:44,368][com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55] - Init DruidDataSource
[INFO][SIRM][9389,1,main][2025-06-24 08:49:46,630][com.alibaba.druid.pool.DruidDataSource.init:972] - {dataSource-1} inited
[INFO][SIRM][9389,1,main][2025-06-24 08:49:54,418][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.<init>:38] - flyway jdbcUrl: **********************************************************?useUnicode=true&characterEncoding=utf8&currentSchema=public&stringtype=unspecified
[INFO][SIRM][9389,1,main][2025-06-24 08:49:55,645][com.sinitek.sirm.ip.limit.interceptor.IpWhiteListInterceptor.init:56] - 当前ip限制状态为: false, true为开启状态,false为关闭状态
[INFO][SIRM][9389,1,main][2025-06-24 08:49:56,699][com.sinitek.sirm.config.SpringMessageConfig.localeChangeInterceptor:123] - 国际化开启状态: false, 系统默认的语言环境为: zh_CN,当前系统支持的语言环境列表: [LocaleInfo(locale=zh_CN, localeName=简体中文, localeChineseName=简体中文), LocaleInfo(locale=zh_HK, localeName=繁體中文, localeChineseName=繁体中文)]
[INFO][SIRM][9389,1,main][2025-06-24 08:49:57,289][springfox.documentation.spring.web.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[INFO][SIRM][9389,1,main][2025-06-24 08:49:57,612][com.sinitek.cloud.base.config.CloudSiniCubeConfig.currentUserInfo:37] - 当前com.sinitek.sirm.common.user.bridging.ICurrentUserInfo的实现类为: com.sinitek.cloud.base.user.CloudServerCurrentUserInfoImpl
[INFO][SIRM][9389,1,main][2025-06-24 08:50:00,856][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:46] - 开始检查有没有执行错误的脚本，如果有则先清理掉
[INFO][SIRM][9389,1,main][2025-06-24 08:50:00,881][com.zaxxer.hikari.HikariDataSource.getConnection:110] - HikariPool-1 - Starting...
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,051][com.zaxxer.hikari.HikariDataSource.getConnection:123] - HikariPool-1 - Start completed.
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,116][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,117][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Database: ********************************************************** (PostgreSQL 13.2)
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,512][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Repair of failed migration in Schema History table "public"."flyway_schema_history" not necessary. No failed migration detected.
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,611][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Successfully repaired schema history table "public"."flyway_schema_history" (execution time 00:00.327s).
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,668][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$cleanMigrateStrategy$0:48] - 开始执行SQL脚本
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,694][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,895][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Current version of schema "public": 100.20250605.01
[INFO][SIRM][9389,1,main][2025-06-24 08:50:01,905][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Schema "public" is up to date. No migration necessary.
[INFO][SIRM][9389,1,main][2025-06-24 08:50:02,707][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][9389,1,main][2025-06-24 08:50:02,707][com.zaxxer.hikari.HikariDataSource.close:350] - HikariPool-1 - Shutdown initiated...
[INFO][SIRM][9389,1,main][2025-06-24 08:50:02,734][com.zaxxer.hikari.HikariDataSource.close:352] - HikariPool-1 - Shutdown completed.
[INFO][SIRM][9389,1,main][2025-06-24 08:50:02,865][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220] - Tomcat started on port(s): 8097 (http) with context path '/zhida'
[INFO][SIRM][9389,1,main][2025-06-24 08:50:02,926][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][9389,1,main][2025-06-24 08:50:02,928][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO][SIRM][9389,1,main][2025-06-24 08:50:03,355][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76] - nacos registry, DEFAULT_GROUP SINITEK-NOCODE-BACKEND **************:8097 register finished
[INFO][SIRM][9389,1,main][2025-06-24 08:50:03,387][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:93] - Documentation plugins bootstrapped
[INFO][SIRM][9389,1,main][2025-06-24 08:50:03,421][springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79] - Found 2 custom documentation plugin(s)
[INFO][SIRM][9389,1,main][2025-06-24 08:50:03,567][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][9389,1,main][2025-06-24 08:50:03,868][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: findOpenApiListUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:04,253][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:04,903][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:04,924][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:04,950][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,097][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,260][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,294][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,307][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,314][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,342][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,390][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,704][springfox.documentation.spring.web.scanners.ApiListingReferenceScanner.scan:44] - Scanning for api listing references
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,746][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,759][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,768][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: platformParamUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,772][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: loginUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,820][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageAuthUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,846][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getBaseSettingUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,854][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getInfoByCustomUrlUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,875][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveUsingPOST_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,891][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveGroupUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,906][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPageSceneUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,916][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,922][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,931][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deletePageAuthUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,940][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveBaseSettingUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,974][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageSceneUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:05,995][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,002][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: copyPageAuthUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,027][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdatePageAuthUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,048][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,053][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFieldAuthDataUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,055][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFirstFormCodeUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,058][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getNameByCodeUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,059][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getUrlByCodeUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,069][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: sceneTypeListUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,077][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupListUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,094][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: groupTreeUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,097][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,112][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: moveUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,139][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getAllFormUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,162][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchPageAuthListUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,178][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getBaseInfoUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,185][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewSecretUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,204][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: settingUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,207][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_4
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,215][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,225][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateNameUsingPOST_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,229][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateStatusUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,233][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: createUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,249][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: customUrlUsingPOST_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,268][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,276][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: accessTokenUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,284][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getEnumerateUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,297][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getManageFormButtonsUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,309][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: applyHistoryVersionUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,312][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormPageDataUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,316][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getPublishedFormUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,319][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateVersionUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,321][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_2
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,325][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_4
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,332][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getIdleTableLengthUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,339][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: effectVersionUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,341][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: publishUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,349][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveFiledConfigUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,360][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFormDataManageSettingUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,369][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: versionHistoryUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,376][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: updateHistoryUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,382][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getFieldMappingCodeUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,420][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: getTemporaryUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,427][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: viewUsingGET_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,434][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: pageFormDataFillReportUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,438][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteUsingPOST_5
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,445][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: deleteBatchUsingPOST_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,451][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: saveOrUpdateUsingPOST_5
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,453][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: temporarySaveUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,459][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: listUsingGET_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,499][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: searchUsingPOST_3
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,505][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: exportUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,516][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: agentGenerateFunctionUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,539][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingGET_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,549][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingHEAD_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,572][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,576][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPUT_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,585][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingPATCH_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,590][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingDELETE_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,597][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingOPTIONS_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,600][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: requestTestUsingTRACE_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,620][springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator.startingWith:41] - Generating unique operation named: agentGenerateDiagramUsingPOST_1
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,664][org.springframework.boot.StartupInfoLogger.logStarted:61] - Started SirmApplication in 42.431 seconds (JVM running for 45.17)
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,687][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs
----------------------------------------------------------
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,688][com.sinitek.sirm.nocode.common.support.swagger.SwaggerJsonUrl.lambda$onApplicationEvent$0:45] - 
----------------------------------------------------------
	 swaggerJson地址 http://***********:8097/zhida/v2/api-docs?group=零代码平台
----------------------------------------------------------
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,697][com.sinitek.sirm.nocode.common.config.PgWallConfig.removePostgreSQLFunctionRestrictions:67] - 已移除0个PostgreSQL JSON函数限制
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,697][com.sinitek.sirm.nocode.common.config.PgWallConfig.lambda$run$0:37] - PostgreSQL Druid WallFilter配置完成
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,699][com.sinitek.data.mybatis.init.EntityNameApplicationRunner.run:68] - 全局实体EntityName初始化完成,总匹配实体数量为: 0,自动设置实体数量为: 0, 请不要在当前代码执行前使用实体.ENTITY_NAME,启动之前可以使用 new 实体名().getEntityNameValue()方式获取到EntityName
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,700][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 对称加密 算法为: AES, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.symmetry.impl.AesSymmetryEncryptionImpl,该类是框架默认实现
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,700][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 非对称加密 算法为: RSA, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.asymmetric.impl.RsaAsymmetricEncryptionImpl,该类是框架默认实现
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,700][com.sinitek.sirm.common.encryption.init.EncryptionAlgorithmCheckRunner.printAlgorithmDescription:107] - 当前使用的 散列 算法为: MD5, 实现类 为: com.sinitek.sirm.common.encryption.algorithm.hash.impl.MD5HashEncryptionImpl,该类是框架默认实现
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,771][com.sinitek.sirm.common.init.SiniCubeUtilsRunner.run:22] - 向ConvertUtils注册DateTimeConverter完成
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,822][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend-local.yml, group=DEFAULT_GROUP
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,831][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend.yml, group=DEFAULT_GROUP
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,831][com.alibaba.cloud.nacos.refresh.NacosContextRefresher.registerNacosListener:131] - [Nacos Config] Listening config: dataId=sinitek-nocode-backend, group=DEFAULT_GROUP
[INFO][SIRM][9389,1,main][2025-06-24 08:50:06,929][com.sinitek.cloud.common.cache.IPCache.getLocalIp:56] - 当前服务器的IPv4地址为: ***********
[INFO][SIRM][9389,108,temp-dir-cleaner-thread][2025-06-24 08:50:06,946][com.sinitek.sirm.common.tempdir.support.TempDirCleaner.run:55] - 自动清理临时目录功能启用状态：false
[INFO][SIRM][9389,1,main][2025-06-24 08:50:07,003][com.sinitek.sirm.nocode.utils.ApplicationStartUtil.startBackendApplication:29] - 
----------------------------------------------------------
	 http://***********:8097/zhida 运行成功
----------------------------------------------------------
[INFO][SIRM][9389,105,SinicubeThreadExecutor-1][2025-06-24 08:50:07,727][com.sinitek.data.model.recheck.support.RecheckSupport.createInstance:39] - 成功初始化复核模型，加载了0个复核实体定义
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:52:06,260][org.springframework.web.servlet.FrameworkServlet.initServletBean:525] - Initializing Servlet 'dispatcherServlet'
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:52:06,271][org.springframework.web.servlet.FrameworkServlet.initServletBean:547] - Completed initialization in 8 ms
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:23,099][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:137] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计一下数据提交数量，按天统计
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:23,103][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计一下数据提交数量，按天统计
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:34,954][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 168
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:34,958][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:98] - 执行动态SQL查询: SELECT 
  DATE_TRUNC('day', createtimestamp) AS submit_day,
  COUNT(*) AS submit_count
FROM 
  public.zd_page_form_data_64
GROUP BY 
  submit_day
ORDER BY 
  submit_day
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:35,055][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:124] - 查询完成，返回2条记录
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:35,099][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：统计一下数据提交数量，按天统计 

数据：[{"submitDay":1749571200000,"submitCount":1},{"submitDay":1750348800000,"submitCount":1}] 

请根据上面的需求和数据生成mermaid代码。
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:57,880][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:486] - Mermaid图表生成完成，代码长度: 116
[INFO][SIRM][9389,84,http-nio-8097-exec-10][2025-06-24 08:52:57,882][com.sinitek.sirm.nocode.controller.ZdLlmController.executeGenerateDiagram:195] - 图表生成完成，会话ID: null
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:54:07,530][com.sinitek.sirm.nocode.controller.ZdLlmController.agentGenerateDiagram:137] - 开始生成图表，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计一下数据提交数量，按天统计
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:54:07,534][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:343] - 开始生成SQL，表单代码: page_36f15be5f7af4f43a8229d8703e05133, 需求: 统计一下数据提交数量，按天统计
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:54:17,007][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.formSqlGenerate:353] - SQL生成完成，表单代码: page_36f15be5f7af4f43a8229d8703e05133, SQL长度: 168
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:54:17,009][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:98] - 执行动态SQL查询: SELECT 
  DATE(createtimestamp) AS submit_date,
  COUNT(*) AS submit_count
FROM 
  public.zd_page_form_data_64
GROUP BY 
  DATE(createtimestamp)
ORDER BY 
  submit_date
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:54:17,044][com.sinitek.sirm.nocode.support.util.SqlQueryUtil.executeQuery:124] - 查询完成，返回2条记录
[WARN][SIRM][9389,19,Thread-2][2025-06-24 08:56:01,145][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:102] - [HttpClientBeanHolder] Start destroying common HttpClient
[WARN][SIRM][9389,28,Thread-8][2025-06-24 08:56:01,138][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:136] - [NotifyCenter] Start destroying Publisher
[INFO][SIRM][9389,75,http-nio-8097-exec-1][2025-06-24 08:56:01,172][com.sinitek.sirm.nocode.service.impl.LlmServiceImpl.mermaidGenerate:475] - 开始生成Mermaid图表，用户需求: 需求：统计一下数据提交数量，按天统计 

数据：[{"submitCount":1,"submitDate":"2025-06-11 00:00:00"},{"submitCount":1,"submitDate":"2025-06-20 00:00:00"}] 

请根据上面的需求和数据生成mermaid代码。
[WARN][SIRM][9389,19,Thread-2][2025-06-24 08:56:01,175][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:111] - [HttpClientBeanHolder] Destruction of the end
[WARN][SIRM][9389,28,Thread-8][2025-06-24 08:56:01,174][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:153] - [NotifyCenter] Destruction of the end
[INFO][SIRM][9389,44,SpringApplicationShutdownHook][2025-06-24 08:56:09,510][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:95] - De-registering from Nacos Server now...
[INFO][SIRM][9389,44,SpringApplicationShutdownHook][2025-06-24 08:56:09,598][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:115] - De-registration finished.
[INFO][SIRM][9389,44,SpringApplicationShutdownHook][2025-06-24 08:56:09,630][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][9389,44,SpringApplicationShutdownHook][2025-06-24 08:56:09,697][com.alibaba.druid.pool.DruidDataSource.close:2051] - {dataSource-1} closing ...
[INFO][SIRM][9389,44,SpringApplicationShutdownHook][2025-06-24 08:56:09,716][com.alibaba.druid.pool.DruidDataSource.close:2124] - {dataSource-1} closed
[INFO][SIRM][13837,1,main][2025-06-24 08:59:33,329][com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[INFO][SIRM][13837,17,background-preinit][2025-06-24 08:59:33,349][org.hibernate.validator.internal.util.Version.<clinit>:21] - HV000001: Hibernate Validator 6.2.5.Final
[INFO][SIRM][21778,1,main][2025-06-24 09:06:47,894][com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[INFO][SIRM][21778,24,background-preinit][2025-06-24 09:06:47,932][org.hibernate.validator.internal.util.Version.<clinit>:21] - HV000001: Hibernate Validator 6.2.5.Final
[INFO][SIRM][21778,1,main][2025-06-24 09:06:49,715][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][21778,1,main][2025-06-24 09:06:49,716][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[WARN][SIRM][21778,1,main][2025-06-24 09:06:52,715][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend] & group[DEFAULT_GROUP]
[WARN][SIRM][21778,1,main][2025-06-24 09:06:52,891][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend-local.yml] & group[DEFAULT_GROUP]
[INFO][SIRM][21778,1,main][2025-06-24 09:06:52,894][org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.doInitialize:134] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP'}]
[INFO][SIRM][21778,1,main][2025-06-24 09:06:53,098][org.springframework.boot.SpringApplication.logStartupProfileInfo:651] - The following 1 profile is active: "local"
[INFO][SIRM][21778,1,main][2025-06-24 09:06:59,556][org.springframework.cloud.context.scope.GenericScope.setSerializationId:283] - BeanFactory id=5df1ba75-22e4-3069-84e7-cd414c7c024c
[INFO][SIRM][21778,1,main][2025-06-24 09:06:59,976][com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39] - Post-processing PropertySource instances
[INFO][SIRM][21778,1,main][2025-06-24 09:06:59,981][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:06:59,981][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:06:59,981][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,044][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,046][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,046][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,047][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,047][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,047][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,047][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,048][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,048][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,048][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap-local.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,048][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,048][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,105][com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2:31] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,241][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'hibernateValidateConfig' of type [com.sinitek.sirm.config.HibernateValidateConfig$$EnhancerBySpringCGLIB$$e213e7e7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,244][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'springMessageConfig' of type [com.sinitek.sirm.config.SpringMessageConfig$$EnhancerBySpringCGLIB$$cac497d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,268][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,273][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,275][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$504/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,292][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,305][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,307][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,352][com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2:35] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,358][com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2:35] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,430][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'i18nProperties' of type [com.sinitek.sirm.i18n.properties.I18nProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,449][com.sinitek.sirm.config.SpringMessageConfig.messageSource:52] - 加载默认消息资源文件：classpath*:message/messages-*.properties
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,480][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'messageSource' of type [org.springframework.context.support.ReloadableResourceBundleMessageSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:00,527][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'validator' of type [org.springframework.validation.beanvalidation.LocalValidatorFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:01,862][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'cacheConfig' of type [com.sinitek.sirm.config.CacheConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:03,751][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108] - Tomcat initialized with port(s): 8097 (http)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:04,188][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:290] - Root WebApplicationContext: initialization completed in 10953 ms
[INFO][SIRM][21778,1,main][2025-06-24 09:07:07,195][com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55] - Init DruidDataSource
[INFO][SIRM][21778,1,main][2025-06-24 09:07:09,509][com.alibaba.druid.pool.DruidDataSource.init:972] - {dataSource-1} inited
[INFO][SIRM][21778,1,main][2025-06-24 09:07:17,995][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.<init>:38] - flyway jdbcUrl: **********************************************************?useUnicode=true&characterEncoding=utf8&currentSchema=public&stringtype=unspecified
[INFO][SIRM][21778,1,main][2025-06-24 09:07:18,989][com.sinitek.sirm.ip.limit.interceptor.IpWhiteListInterceptor.init:56] - 当前ip限制状态为: false, true为开启状态,false为关闭状态
[INFO][SIRM][21778,1,main][2025-06-24 09:07:20,164][com.sinitek.sirm.config.SpringMessageConfig.localeChangeInterceptor:123] - 国际化开启状态: false, 系统默认的语言环境为: zh_CN,当前系统支持的语言环境列表: [LocaleInfo(locale=zh_CN, localeName=简体中文, localeChineseName=简体中文), LocaleInfo(locale=zh_HK, localeName=繁體中文, localeChineseName=繁体中文)]
[INFO][SIRM][21778,1,main][2025-06-24 09:07:22,112][org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver.<init>:58] - Exposing 19 endpoint(s) beneath base path '/actuator'
[INFO][SIRM][21778,1,main][2025-06-24 09:07:22,551][springfox.documentation.spring.web.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[INFO][SIRM][21778,1,main][2025-06-24 09:07:22,767][com.sinitek.cloud.base.config.CloudSiniCubeConfig.currentUserInfo:37] - 当前com.sinitek.sirm.common.user.bridging.ICurrentUserInfo的实现类为: com.sinitek.cloud.base.user.CloudServerCurrentUserInfoImpl
[INFO][SIRM][21778,1,main][2025-06-24 09:07:25,601][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$0:46] - 开始检查有没有执行错误的脚本，如果有则先清理掉
[INFO][SIRM][21778,1,main][2025-06-24 09:07:25,633][com.zaxxer.hikari.HikariDataSource.getConnection:110] - HikariPool-1 - Starting...
[INFO][SIRM][21778,1,main][2025-06-24 09:07:25,808][com.zaxxer.hikari.HikariDataSource.getConnection:123] - HikariPool-1 - Start completed.
[INFO][SIRM][21778,1,main][2025-06-24 09:07:25,886][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][21778,1,main][2025-06-24 09:07:25,887][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Database: ********************************************************** (PostgreSQL 13.2)
[INFO][SIRM][21778,1,main][2025-06-24 09:07:26,093][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Repair of failed migration in Schema History table "public"."flyway_schema_history" not necessary. No failed migration detected.
[INFO][SIRM][21778,1,main][2025-06-24 09:07:26,169][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Successfully repaired schema history table "public"."flyway_schema_history" (execution time 00:00.151s).
[INFO][SIRM][21778,1,main][2025-06-24 09:07:26,209][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$0:48] - 开始执行SQL脚本
[INFO][SIRM][21778,1,main][2025-06-24 09:07:26,242][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][21778,1,main][2025-06-24 09:07:26,446][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Current version of schema "public": 100.20250605.01
[INFO][SIRM][21778,1,main][2025-06-24 09:07:26,457][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Schema "public" is up to date. No migration necessary.
[INFO][SIRM][21778,1,main][2025-06-24 09:07:27,358][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][21778,1,main][2025-06-24 09:07:27,359][com.zaxxer.hikari.HikariDataSource.close:350] - HikariPool-1 - Shutdown initiated...
[INFO][SIRM][21778,1,main][2025-06-24 09:07:27,381][com.zaxxer.hikari.HikariDataSource.close:352] - HikariPool-1 - Shutdown completed.
[INFO][SIRM][21778,1,main][2025-06-24 09:07:27,680][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220] - Tomcat started on port(s): 8097 (http) with context path '/zhida'
[INFO][SIRM][21778,1,main][2025-06-24 09:07:27,735][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][21778,1,main][2025-06-24 09:07:27,736][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,137][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76] - nacos registry, DEFAULT_GROUP SINITEK-NOCODE-BACKEND **************:8097 register finished
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,161][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:93] - Documentation plugins bootstrapped
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,173][springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79] - Found 2 custom documentation plugin(s)
[WARN][SIRM][21778,1,main][2025-06-24 09:07:28,181][org.springframework.context.support.AbstractApplicationContext.refresh:599] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'documentationPluginsBootstrapper'; nested exception is java.lang.NullPointerException
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,185][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:95] - De-registering from Nacos Server now...
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,202][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:115] - De-registration finished.
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,562][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,617][com.alibaba.druid.pool.DruidDataSource.close:2051] - {dataSource-1} closing ...
[INFO][SIRM][21778,1,main][2025-06-24 09:07:28,639][com.alibaba.druid.pool.DruidDataSource.close:2124] - {dataSource-1} closed
[INFO][SIRM][21778,1,main][2025-06-24 09:07:29,793][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[ERROR][SIRM][21778,1,main][2025-06-24 09:07:29,864][org.springframework.boot.SpringApplication.reportFailure:835] - Application run failed
org.springframework.context.ApplicationContextException: Failed to start bean 'documentationPluginsBootstrapper'; nested exception is java.lang.NullPointerException
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:186) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:58) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:365) ~[spring-context-5.3.39.jar:5.3.39]
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:1.8.0_452]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:160) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:128) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:949) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:423) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.15.jar:2.6.15]
	at com.sinitek.sirm.nocode.utils.ApplicationStartUtil.startBackendApplication(ApplicationStartUtil.java:27) ~[classes/:?]
	at com.sinitek.sirm.nocode.SirmApplication.main(SirmApplication.java:28) ~[classes/:?]
Caused by: java.lang.NullPointerException
	at springfox.documentation.spring.web.WebMvcPatternsRequestConditionWrapper.getPatterns(WebMvcPatternsRequestConditionWrapper.java:56) ~[springfox-spring-webmvc-2.10.5.jar:null]
	at springfox.documentation.RequestHandler.sortedPaths(RequestHandler.java:112) ~[springfox-core-2.10.5.jar:null]
	at springfox.documentation.spi.service.contexts.Orderings.lambda$byPatternsCondition$3(Orderings.java:89) ~[springfox-spi-2.10.5.jar:null]
	at java.util.Comparator.lambda$comparing$77a9974f$1(Comparator.java:469) ~[?:1.8.0_452]
	at java.util.TimSort.countRunAndMakeAscending(TimSort.java:355) ~[?:1.8.0_452]
	at java.util.TimSort.sort(TimSort.java:234) ~[?:1.8.0_452]
	at java.util.Arrays.sort(Arrays.java:1512) ~[?:1.8.0_452]
	at java.util.ArrayList.sort(ArrayList.java:1464) ~[?:1.8.0_452]
	at java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:392) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at springfox.documentation.spring.web.plugins.WebMvcRequestHandlerProvider.requestHandlers(WebMvcRequestHandlerProvider.java:76) ~[springfox-spring-webmvc-2.10.5.jar:null]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.defaultContextBuilder(AbstractDocumentationPluginsBootstrapper.java:108) ~[springfox-spring-web-2.10.5.jar:null]
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.buildContext(AbstractDocumentationPluginsBootstrapper.java:92) ~[springfox-spring-web-2.10.5.jar:null]
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins(AbstractDocumentationPluginsBootstrapper.java:83) ~[springfox-spring-web-2.10.5.jar:null]
	at springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start(DocumentationPluginsBootstrapper.java:94) ~[springfox-spring-web-2.10.5.jar:null]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:183) ~[spring-context-5.3.39.jar:5.3.39]
	... 15 more
[WARN][SIRM][21778,25,Thread-3][2025-06-24 09:07:29,883][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:102] - [HttpClientBeanHolder] Start destroying common HttpClient
[WARN][SIRM][21778,34,Thread-9][2025-06-24 09:07:29,884][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:136] - [NotifyCenter] Start destroying Publisher
[WARN][SIRM][21778,25,Thread-3][2025-06-24 09:07:29,886][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:111] - [HttpClientBeanHolder] Destruction of the end
[WARN][SIRM][21778,34,Thread-9][2025-06-24 09:07:29,887][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:153] - [NotifyCenter] Destruction of the end
[INFO][SIRM][24715,1,main][2025-06-24 09:11:48,801][com.alibaba.nacos.client.env.SearchableProperties.sortPropertySourceDefaultOrder:197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[INFO][SIRM][24715,24,background-preinit][2025-06-24 09:11:48,830][org.hibernate.validator.internal.util.Version.<clinit>:21] - HV000001: Hibernate Validator 6.2.5.Final
[INFO][SIRM][24715,1,main][2025-06-24 09:11:50,520][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][24715,1,main][2025-06-24 09:11:50,520][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[WARN][SIRM][24715,1,main][2025-06-24 09:11:53,360][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend] & group[DEFAULT_GROUP]
[WARN][SIRM][24715,1,main][2025-06-24 09:11:53,538][com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData:97] - Ignore the empty nacos configuration and get it based on dataId[sinitek-nocode-backend-local.yml] & group[DEFAULT_GROUP]
[INFO][SIRM][24715,1,main][2025-06-24 09:11:53,546][org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.doInitialize:134] - Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP'}]
[INFO][SIRM][24715,1,main][2025-06-24 09:11:53,747][org.springframework.boot.SpringApplication.logStartupProfileInfo:651] - The following 1 profile is active: "local"
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,177][org.springframework.cloud.context.scope.GenericScope.setSerializationId:283] - BeanFactory id=5df1ba75-22e4-3069-84e7-cd414c7c024c
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,567][com.ulisesbocchio.jasyptspringboot.configuration.EnableEncryptablePropertiesBeanFactoryPostProcessor.postProcessBeanFactory:39] - Post-processing PropertySource instances
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,588][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend-local.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,589][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,589][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource bootstrapProperties-sinitek-nocode-backend,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,688][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,689][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,690][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,690][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,691][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,691][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,692][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,692][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,692][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,692][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap-local.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,692][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,693][com.ulisesbocchio.jasyptspringboot.EncryptablePropertySourceConverter.makeEncryptable:56] - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[INFO][SIRM][24715,1,main][2025-06-24 09:12:00,755][com.ulisesbocchio.jasyptspringboot.filter.DefaultLazyPropertyFilter.lambda$null$2:31] - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,030][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'hibernateValidateConfig' of type [com.sinitek.sirm.config.HibernateValidateConfig$$EnhancerBySpringCGLIB$$e213e7e7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,041][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'springMessageConfig' of type [com.sinitek.sirm.config.SpringMessageConfig$$EnhancerBySpringCGLIB$$cac497d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,070][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,075][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,078][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$504/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,089][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,105][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,107][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,190][com.ulisesbocchio.jasyptspringboot.resolver.DefaultLazyPropertyResolver.lambda$null$2:35] - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,209][com.ulisesbocchio.jasyptspringboot.detector.DefaultLazyPropertyDetector.lambda$null$2:35] - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,268][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'i18nProperties' of type [com.sinitek.sirm.i18n.properties.I18nProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,309][com.sinitek.sirm.config.SpringMessageConfig.messageSource:52] - 加载默认消息资源文件：classpath*:message/messages-*.properties
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,338][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'messageSource' of type [org.springframework.context.support.ReloadableResourceBundleMessageSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:01,398][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'validator' of type [org.springframework.validation.beanvalidation.LocalValidatorFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:02,858][org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker.postProcessAfterInitialization:376] - Bean 'cacheConfig' of type [com.sinitek.sirm.config.CacheConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:04,870][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize:108] - Tomcat initialized with port(s): 8097 (http)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:05,266][org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.prepareWebApplicationContext:290] - Root WebApplicationContext: initialization completed in 11334 ms
[INFO][SIRM][24715,1,main][2025-06-24 09:12:07,944][com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.dataSource:55] - Init DruidDataSource
[INFO][SIRM][24715,1,main][2025-06-24 09:12:12,938][com.alibaba.druid.pool.DruidDataSource.init:972] - {dataSource-1} inited
[INFO][SIRM][24715,1,main][2025-06-24 09:12:21,318][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.<init>:38] - flyway jdbcUrl: **********************************************************?useUnicode=true&characterEncoding=utf8&currentSchema=public&stringtype=unspecified
[INFO][SIRM][24715,1,main][2025-06-24 09:12:22,275][com.sinitek.sirm.ip.limit.interceptor.IpWhiteListInterceptor.init:56] - 当前ip限制状态为: false, true为开启状态,false为关闭状态
[INFO][SIRM][24715,1,main][2025-06-24 09:12:23,390][com.sinitek.sirm.config.SpringMessageConfig.localeChangeInterceptor:123] - 国际化开启状态: false, 系统默认的语言环境为: zh_CN,当前系统支持的语言环境列表: [LocaleInfo(locale=zh_CN, localeName=简体中文, localeChineseName=简体中文), LocaleInfo(locale=zh_HK, localeName=繁體中文, localeChineseName=繁体中文)]
[INFO][SIRM][24715,1,main][2025-06-24 09:12:24,970][org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver.<init>:58] - Exposing 19 endpoint(s) beneath base path '/actuator'
[INFO][SIRM][24715,1,main][2025-06-24 09:12:25,276][springfox.documentation.spring.web.WebMvcPropertySourcedRequestMappingHandlerMapping.initHandlerMethods:69] - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
[INFO][SIRM][24715,1,main][2025-06-24 09:12:25,413][com.sinitek.cloud.base.config.CloudSiniCubeConfig.currentUserInfo:37] - 当前com.sinitek.sirm.common.user.bridging.ICurrentUserInfo的实现类为: com.sinitek.cloud.base.user.CloudServerCurrentUserInfoImpl
[INFO][SIRM][24715,1,main][2025-06-24 09:12:28,178][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$0:46] - 开始检查有没有执行错误的脚本，如果有则先清理掉
[INFO][SIRM][24715,1,main][2025-06-24 09:12:28,210][com.zaxxer.hikari.HikariDataSource.getConnection:110] - HikariPool-1 - Starting...
[INFO][SIRM][24715,1,main][2025-06-24 09:12:29,025][com.zaxxer.hikari.HikariDataSource.getConnection:123] - HikariPool-1 - Start completed.
[INFO][SIRM][24715,1,main][2025-06-24 09:12:29,264][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][24715,1,main][2025-06-24 09:12:29,266][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Database: ********************************************************** (PostgreSQL 13.2)
[INFO][SIRM][24715,1,main][2025-06-24 09:12:29,908][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Repair of failed migration in Schema History table "public"."flyway_schema_history" not necessary. No failed migration detected.
[INFO][SIRM][24715,1,main][2025-06-24 09:12:29,984][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Successfully repaired schema history table "public"."flyway_schema_history" (execution time 00:00.169s).
[INFO][SIRM][24715,1,main][2025-06-24 09:12:30,029][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.lambda$0:48] - 开始执行SQL脚本
[INFO][SIRM][24715,1,main][2025-06-24 09:12:30,052][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Flyway Community Edition 8.0.5 by Redgate
[INFO][SIRM][24715,1,main][2025-06-24 09:12:30,265][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Current version of schema "public": 100.20250605.01
[INFO][SIRM][24715,1,main][2025-06-24 09:12:30,276][org.flywaydb.core.internal.logging.slf4j.Slf4jLog.info:37] - Schema "public" is up to date. No migration necessary.
[INFO][SIRM][24715,1,main][2025-06-24 09:12:31,202][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][24715,1,main][2025-06-24 09:12:31,203][com.zaxxer.hikari.HikariDataSource.close:350] - HikariPool-1 - Shutdown initiated...
[INFO][SIRM][24715,1,main][2025-06-24 09:12:31,226][com.zaxxer.hikari.HikariDataSource.close:352] - HikariPool-1 - Shutdown completed.
[INFO][SIRM][24715,1,main][2025-06-24 09:12:31,534][org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start:220] - Tomcat started on port(s): 8097 (http) with context path '/zhida'
[INFO][SIRM][24715,1,main][2025-06-24 09:12:31,583][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[INFO][SIRM][24715,1,main][2025-06-24 09:12:31,583][com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager.init:56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,019][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.register:76] - nacos registry, DEFAULT_GROUP SINITEK-NOCODE-BACKEND **************:8097 register finished
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,059][springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start:93] - Documentation plugins bootstrapped
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,095][springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins:79] - Found 2 custom documentation plugin(s)
[WARN][SIRM][24715,1,main][2025-06-24 09:12:32,103][org.springframework.context.support.AbstractApplicationContext.refresh:599] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'documentationPluginsBootstrapper'; nested exception is java.lang.NullPointerException
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,110][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:95] - De-registering from Nacos Server now...
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,135][com.alibaba.cloud.nacos.registry.NacosServiceRegistry.deregister:115] - De-registration finished.
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,474][com.sinitek.sirm.nocode.support.autoconfigure.FlywayAutoConfiguration.close:56] - 关闭flyway数据源
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,544][com.alibaba.druid.pool.DruidDataSource.close:2051] - {dataSource-1} closing ...
[INFO][SIRM][24715,1,main][2025-06-24 09:12:32,571][com.alibaba.druid.pool.DruidDataSource.close:2124] - {dataSource-1} closed
[INFO][SIRM][24715,1,main][2025-06-24 09:12:33,698][org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener.logMessage:136] - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
[ERROR][SIRM][24715,1,main][2025-06-24 09:12:33,739][org.springframework.boot.SpringApplication.reportFailure:835] - Application run failed
org.springframework.context.ApplicationContextException: Failed to start bean 'documentationPluginsBootstrapper'; nested exception is java.lang.NullPointerException
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:186) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.DefaultLifecycleProcessor.access$200(DefaultLifecycleProcessor.java:58) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.start(DefaultLifecycleProcessor.java:365) ~[spring-context-5.3.39.jar:5.3.39]
	at java.lang.Iterable.forEach(Iterable.java:75) ~[?:1.8.0_452]
	at org.springframework.context.support.DefaultLifecycleProcessor.startBeans(DefaultLifecycleProcessor.java:160) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.DefaultLifecycleProcessor.onRefresh(DefaultLifecycleProcessor.java:128) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.AbstractApplicationContext.finishRefresh(AbstractApplicationContext.java:949) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:594) ~[spring-context-5.3.39.jar:5.3.39]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:423) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1317) ~[spring-boot-2.6.15.jar:2.6.15]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-2.6.15.jar:2.6.15]
	at com.sinitek.sirm.nocode.utils.ApplicationStartUtil.startBackendApplication(ApplicationStartUtil.java:27) ~[classes/:?]
	at com.sinitek.sirm.nocode.SirmApplication.main(SirmApplication.java:28) ~[classes/:?]
Caused by: java.lang.NullPointerException
	at springfox.documentation.spring.web.WebMvcPatternsRequestConditionWrapper.getPatterns(WebMvcPatternsRequestConditionWrapper.java:56) ~[springfox-spring-webmvc-2.10.5.jar:null]
	at springfox.documentation.RequestHandler.sortedPaths(RequestHandler.java:112) ~[springfox-core-2.10.5.jar:null]
	at springfox.documentation.spi.service.contexts.Orderings.lambda$byPatternsCondition$3(Orderings.java:89) ~[springfox-spi-2.10.5.jar:null]
	at java.util.Comparator.lambda$comparing$77a9974f$1(Comparator.java:469) ~[?:1.8.0_452]
	at java.util.TimSort.countRunAndMakeAscending(TimSort.java:355) ~[?:1.8.0_452]
	at java.util.TimSort.sort(TimSort.java:234) ~[?:1.8.0_452]
	at java.util.Arrays.sort(Arrays.java:1512) ~[?:1.8.0_452]
	at java.util.ArrayList.sort(ArrayList.java:1464) ~[?:1.8.0_452]
	at java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:392) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.Sink$ChainedReference.end(Sink.java:258) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at springfox.documentation.spring.web.plugins.WebMvcRequestHandlerProvider.requestHandlers(WebMvcRequestHandlerProvider.java:76) ~[springfox-spring-webmvc-2.10.5.jar:null]
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193) ~[?:1.8.0_452]
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472) ~[?:1.8.0_452]
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708) ~[?:1.8.0_452]
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[?:1.8.0_452]
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566) ~[?:1.8.0_452]
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.defaultContextBuilder(AbstractDocumentationPluginsBootstrapper.java:108) ~[springfox-spring-web-2.10.5.jar:null]
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.buildContext(AbstractDocumentationPluginsBootstrapper.java:92) ~[springfox-spring-web-2.10.5.jar:null]
	at springfox.documentation.spring.web.plugins.AbstractDocumentationPluginsBootstrapper.bootstrapDocumentationPlugins(AbstractDocumentationPluginsBootstrapper.java:83) ~[springfox-spring-web-2.10.5.jar:null]
	at springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper.start(DocumentationPluginsBootstrapper.java:94) ~[springfox-spring-web-2.10.5.jar:null]
	at org.springframework.context.support.DefaultLifecycleProcessor.doStart(DefaultLifecycleProcessor.java:183) ~[spring-context-5.3.39.jar:5.3.39]
	... 15 more
[WARN][SIRM][24715,25,Thread-3][2025-06-24 09:12:33,753][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:102] - [HttpClientBeanHolder] Start destroying common HttpClient
[WARN][SIRM][24715,34,Thread-9][2025-06-24 09:12:33,753][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:136] - [NotifyCenter] Start destroying Publisher
[WARN][SIRM][24715,34,Thread-9][2025-06-24 09:12:33,755][com.alibaba.nacos.common.notify.NotifyCenter.shutdown:153] - [NotifyCenter] Destruction of the end
[WARN][SIRM][24715,25,Thread-3][2025-06-24 09:12:33,755][com.alibaba.nacos.common.http.HttpClientBeanHolder.shutdown:111] - [HttpClientBeanHolder] Destruction of the end
