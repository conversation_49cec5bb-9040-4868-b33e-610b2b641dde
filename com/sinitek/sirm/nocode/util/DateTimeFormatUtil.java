package com.sinitek.sirm.nocode.util;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日期时间格式化工具类
 * 用于将时间戳转换为可读的日期格式
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-01
 */
public class DateTimeFormatUtil {

    /**
     * 默认日期时间格式
     */
    private static final String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 默认日期格式
     */
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 预处理数据，将时间戳转换为可读的日期格式
     *
     * @param data 原始数据
     * @return 处理后的数据
     */
    public static List<Map<String, Object>> preprocessDataForDisplay(List<Map<String, Object>> data) {
        if (data == null || data.isEmpty()) {
            return data;
        }

        List<Map<String, Object>> processedData = new ArrayList<>();
        
        for (Map<String, Object> row : data) {
            Map<String, Object> processedRow = new HashMap<>();
            
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                // 处理日期时间字段
                Object processedValue = processDateTimeValue(key, value);
                processedRow.put(key, processedValue);
            }
            
            processedData.add(processedRow);
        }
        
        return processedData;
    }

    /**
     * 处理日期时间值
     *
     * @param fieldName 字段名
     * @param value 字段值
     * @return 处理后的值
     */
    private static Object processDateTimeValue(String fieldName, Object value) {
        if (value == null) {
            return null;
        }

        // 检查是否为日期时间字段（根据字段名判断）
        if (isDateTimeField(fieldName)) {
            return formatDateTimeValue(value);
        }

        // 检查是否为时间戳（数值型且可能是时间戳）
        if (isPossibleTimestamp(value)) {
            return formatTimestampValue(value);
        }

        return value;
    }

    /**
     * 判断字段是否为日期时间字段
     *
     * @param fieldName 字段名
     * @return 是否为日期时间字段
     */
    private static boolean isDateTimeField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("date") || 
               lowerFieldName.contains("time") || 
               lowerFieldName.contains("created") || 
               lowerFieldName.contains("updated") || 
               lowerFieldName.contains("modified") ||
               lowerFieldName.endsWith("_at") ||
               lowerFieldName.endsWith("_on");
    }

    /**
     * 判断值是否可能是时间戳
     *
     * @param value 值
     * @return 是否可能是时间戳
     */
    private static boolean isPossibleTimestamp(Object value) {
        if (!(value instanceof Number)) {
            return false;
        }
        
        long timestamp = ((Number) value).longValue();
        
        // 判断是否在合理的时间戳范围内
        // 1970-01-01 到 2100-01-01 之间的毫秒时间戳
        long minTimestamp = 0L; // 1970-01-01
        long maxTimestamp = 4102444800000L; // 2100-01-01
        
        return timestamp >= minTimestamp && timestamp <= maxTimestamp && timestamp > 1000000000L;
    }

    /**
     * 格式化日期时间值
     *
     * @param value 日期时间值
     * @return 格式化后的字符串
     */
    private static String formatDateTimeValue(Object value) {
        try {
            if (value instanceof Date) {
                return formatDate((Date) value);
            } else if (value instanceof LocalDateTime) {
                return formatLocalDateTime((LocalDateTime) value);
            } else if (value instanceof LocalDate) {
                return formatLocalDate((LocalDate) value);
            } else if (value instanceof Number) {
                return formatTimestamp(((Number) value).longValue());
            } else if (value instanceof String) {
                // 尝试解析字符串形式的时间戳
                try {
                    long timestamp = Long.parseLong((String) value);
                    return formatTimestamp(timestamp);
                } catch (NumberFormatException e) {
                    return (String) value; // 返回原字符串
                }
            }
        } catch (Exception e) {
            System.err.println("日期格式化失败，字段值: " + value + ", 错误: " + e.getMessage());
        }
        
        return value != null ? value.toString() : null;
    }

    /**
     * 格式化时间戳值
     *
     * @param value 时间戳值
     * @return 格式化后的字符串
     */
    private static String formatTimestampValue(Object value) {
        try {
            long timestamp = ((Number) value).longValue();
            return formatTimestamp(timestamp);
        } catch (Exception e) {
            System.err.println("时间戳格式化失败，值: " + value + ", 错误: " + e.getMessage());
            return value.toString();
        }
    }

    /**
     * 格式化时间戳为可读日期格式
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期字符串
     */
    public static String formatTimestamp(long timestamp) {
        try {
            Date date = new Date(timestamp);
            SimpleDateFormat sdf = new SimpleDateFormat(DEFAULT_DATETIME_FORMAT);
            return sdf.format(date);
        } catch (Exception e) {
            System.err.println("时间戳格式化失败: " + timestamp);
            return String.valueOf(timestamp);
        }
    }

    /**
     * 格式化Date对象
     *
     * @param date Date对象
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DEFAULT_DATETIME_FORMAT);
        return sdf.format(date);
    }

    /**
     * 格式化LocalDateTime对象
     *
     * @param dateTime LocalDateTime对象
     * @return 格式化后的日期字符串
     */
    public static String formatLocalDateTime(LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DEFAULT_DATETIME_FORMAT);
        return dateTime.format(formatter);
    }

    /**
     * 格式化LocalDate对象
     *
     * @param date LocalDate对象
     * @return 格式化后的日期字符串
     */
    public static String formatLocalDate(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
        return date.format(formatter);
    }

    /**
     * 将时间戳转换为指定格式的日期字符串
     *
     * @param timestamp 时间戳（毫秒）
     * @param format 日期格式，如 "yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期字符串
     */
    public static String formatTimestamp(long timestamp, String format) {
        try {
            Date date = new Date(timestamp);
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(date);
        } catch (Exception e) {
            System.err.println("时间戳格式化失败: " + timestamp + ", 格式: " + format);
            return String.valueOf(timestamp);
        }
    }

    /**
     * 检查并转换单个值
     *
     * @param value 要检查的值
     * @return 转换后的值
     */
    public static Object convertTimestampValue(Object value) {
        if (value == null) {
            return null;
        }

        if (isPossibleTimestamp(value)) {
            return formatTimestampValue(value);
        }

        return value;
    }
}
