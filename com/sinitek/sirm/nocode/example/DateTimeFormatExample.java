package com.sinitek.sirm.nocode.example;

import com.sinitek.sirm.nocode.util.DateTimeFormatUtil;
import cn.hutool.json.JSONUtil;
import java.util.*;

/**
 * 日期时间格式化使用示例
 * 演示如何将时间戳转换为可读的日期格式
 */
public class DateTimeFormatExample {

    public static void main(String[] args) {
        // 模拟从数据库查询得到的数据，包含时间戳
        List<Map<String, Object>> originalData = createSampleData();
        
        System.out.println("=== 原始数据（包含时间戳）===");
        System.out.println(JSONUtil.toJsonPrettyStr(originalData));
        
        // 使用工具类处理数据
        List<Map<String, Object>> processedData = DateTimeFormatUtil.preprocessDataForDisplay(originalData);
        
        System.out.println("\n=== 处理后的数据（时间戳转换为可读格式）===");
        System.out.println(JSONUtil.toJsonPrettyStr(processedData));
        
        // 演示单个时间戳转换
        System.out.println("\n=== 单个时间戳转换示例 ===");
        long timestamp = 1749571200000L; // 对应 2025-06-23 12:00:00
        String formattedDate = DateTimeFormatUtil.formatTimestamp(timestamp);
        System.out.println("时间戳: " + timestamp + " -> " + formattedDate);
        
        // 演示自定义格式
        String customFormat = DateTimeFormatUtil.formatTimestamp(timestamp, "yyyy年MM月dd日 HH:mm:ss");
        System.out.println("自定义格式: " + customFormat);
    }

    /**
     * 创建包含时间戳的示例数据
     */
    private static List<Map<String, Object>> createSampleData() {
        List<Map<String, Object>> data = new ArrayList<>();
        
        // 第一条记录
        Map<String, Object> record1 = new HashMap<>();
        record1.put("id", 1);
        record1.put("name", "张三");
        record1.put("score", 85.5);
        record1.put("created_time", 1749571200000L); // 2025-06-23 12:00:00
        record1.put("updated_at", 1749574800000L);   // 2025-06-23 13:00:00
        record1.put("birth_date", 631123200000L);    // 1990-01-01 00:00:00
        data.add(record1);
        
        // 第二条记录
        Map<String, Object> record2 = new HashMap<>();
        record2.put("id", 2);
        record2.put("name", "李四");
        record2.put("score", 92.0);
        record2.put("created_time", 1749657600000L); // 2025-06-24 12:00:00
        record2.put("updated_at", 1749661200000L);   // 2025-06-24 13:00:00
        record2.put("birth_date", 662659200000L);    // 1991-01-01 00:00:00
        data.add(record2);
        
        // 第三条记录
        Map<String, Object> record3 = new HashMap<>();
        record3.put("id", 3);
        record3.put("name", "王五");
        record3.put("score", 78.8);
        record3.put("created_time", 1749744000000L); // 2025-06-25 12:00:00
        record3.put("updated_at", 1749747600000L);   // 2025-06-25 13:00:00
        record3.put("birth_date", 694195200000L);    // 1992-01-01 00:00:00
        data.add(record3);
        
        return data;
    }
}

/**
 * 在您的 generateDiagramFromData 方法中的使用方式：
 * 
 * private String generateDiagramFromData(String userPrompt, List<Map<String, Object>> data) {
 *     // 预处理数据，将时间戳转换为可读日期格式
 *     List<Map<String, Object>> processedData = DateTimeFormatUtil.preprocessDataForDisplay(data);
 *     
 *     // 构建图表生成提示词
 *     String diagramPrompt = String.format(
 *         "需求：%s \n\n数据：%s \n\n请根据上面的需求和数据生成mermaid代码。",
 *         userPrompt,
 *         JSONUtil.toJsonStr(processedData)  // 使用处理后的数据
 *     );
 *     
 *     // ... 其余代码保持不变
 * }
 */
