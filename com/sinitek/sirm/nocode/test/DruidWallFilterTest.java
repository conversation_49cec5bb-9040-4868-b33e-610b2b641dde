package com.sinitek.sirm.nocode.test;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.wall.WallConfig;
import com.alibaba.druid.wall.WallFilter;

/**
 * Druid WallFilter测试类
 * 验证PostgreSQL JSON函数是否被正确配置
 */
public class DruidWallFilterTest {

    public static void main(String[] args) {
        // 创建WallConfig
        WallConfig config = new WallConfig();
        
        // 显示默认被禁用的函数
        System.out.println("=== 默认被禁用的函数 ===");
        config.getDenyFunctions().forEach(System.out::println);
        
        // 模拟PgWallConfig的配置
        System.out.println("\n=== 移除PostgreSQL JSON函数限制 ===");
        
        // 移除原有的current_schema限制
        boolean removed1 = config.getDenyFunctions().remove("current_schema");
        System.out.println("移除current_schema: " + removed1);
        
        // 移除PostgreSQL JSON函数限制
        String[] jsonFunctions = {
            "jsonb_array_elements", "jsonb_array_elements_text", 
            "jsonb_path_exists", "jsonb_extract_path", "jsonb_extract_path_text",
            "json_array_elements", "json_array_elements_text",
            "json_extract_path", "json_extract_path_text"
        };
        
        int removedCount = 0;
        for (String function : jsonFunctions) {
            if (config.getDenyFunctions().remove(function)) {
                removedCount++;
                System.out.println("已移除函数限制: " + function);
            }
        }
        
        System.out.println("总共移除了 " + removedCount + " 个PostgreSQL JSON函数限制");
        
        // 显示配置后剩余的被禁用函数
        System.out.println("\n=== 配置后剩余的被禁用函数 ===");
        config.getDenyFunctions().forEach(System.out::println);
        
        // 测试SQL安全检查
        System.out.println("\n=== 测试SQL安全检查 ===");
        testSqlSafety();
    }
    
    private static void testSqlSafety() {
        // 测试复杂的PostgreSQL JSON查询
        String complexSql = """
            SELECT 
                elem->>'ZDInput_jvy2' AS subject,
                AVG((elem->'ZDNumber_wx80')::numeric) AS avg_score
            FROM 
                public.zd_page_form_data_64 
            LEFT JOIN LATERAL jsonb_array_elements(form_data->'model'->'ZDChildForm_zolh') AS elem ON true
            WHERE 
                elem->>'ZDInput_jvy2' IN ('语文', '数学')
            GROUP BY 
                subject
            """;
        
        System.out.println("测试SQL:");
        System.out.println(complexSql);
        
        // 检查是否包含PostgreSQL JSON操作符
        boolean hasJsonOps = containsPostgreSQLJsonOperators(complexSql);
        System.out.println("包含PostgreSQL JSON操作符: " + hasJsonOps);
        
        // 检查是否为安全的SELECT语句
        boolean isSafeSelect = isSafeSelectStatement(complexSql);
        System.out.println("是安全的SELECT语句: " + isSafeSelect);
    }
    
    /**
     * 检查SQL是否包含PostgreSQL特有的JSON操作符
     */
    private static boolean containsPostgreSQLJsonOperators(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        
        String lowerSql = sql.toLowerCase();
        
        // 检查PostgreSQL JSON操作符
        if (lowerSql.contains("->") || lowerSql.contains("->>") || 
            lowerSql.contains("#>") || lowerSql.contains("#>>")) {
            return true;
        }
        
        // 检查PostgreSQL JSON函数
        if (lowerSql.contains("jsonb_array_elements") || 
            lowerSql.contains("jsonb_path_exists") ||
            lowerSql.contains("jsonb_extract_path") ||
            lowerSql.contains("json_array_elements") ||
            lowerSql.contains("lateral")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否为安全的SELECT语句
     */
    private static boolean isSafeSelectStatement(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        
        String cleanSql = sql.trim().toLowerCase();
        
        // 检查是否以SELECT开头
        if (!cleanSql.startsWith("select")) {
            return false;
        }
        
        // 检查是否包含危险关键字
        String[] dangerousKeywords = {
            "insert", "update", "delete", "drop", "create", "alter", 
            "truncate", "exec", "execute", "declare", "merge", "replace", "call"
        };
        
        for (String keyword : dangerousKeywords) {
            if (cleanSql.contains(" " + keyword + " ") || 
                cleanSql.contains(" " + keyword + "(") ||
                cleanSql.endsWith(" " + keyword)) {
                return false;
            }
        }
        
        return true;
    }
}
