package com.sinitek.sirm.nocode.form.dao;

import com.sinitek.sirm.nocode.form.entity.ZdPageForm;
import com.sinitek.sirm.nocode.page.dto.PageDataDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdPageFormTransDAO extends ZdPageForm {

    private List<PageDataDTO> pageDataDTOList;
}
