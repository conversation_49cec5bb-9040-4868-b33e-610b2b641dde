<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.app.mapper.ZdAppMapper">
    <update id="updateStatus">
        update zd_app
        set status = (1 - status)
        where code = #{appCode}
    </update>


    <select id="search" resultType="com.sinitek.sirm.nocode.app.dto.ZdAppDTO">
        select * from zd_app a
        <where>
            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(params.name)">
                <bind name="name_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(params.name)"/>
                and upper(name) like upper(#{name_like}) escape '/'
            </if>
            <if test="params.status!=null">
                and a.status = #{params.status.value}
            </if>
            <if test="params.id!=null">
                and a.id = #{params.id}
            </if>

            <if test="@org.apache.commons.lang3.BooleanUtils@isTrue(params.myCreate)">
                and a.creator_id = #{params.orgId}
            </if>
            and exists( select 1 from zd_app_manager m where m.app_code = a.code and m.org_id = #{params.orgId})
        </where>
    </select>
</mapper>
