<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.form.mapper.ZdPageFormMapper">
    <select id="codeBelongsToAppCode" resultType="java.lang.Boolean">
        select 1
        from zd_page p
                 inner join zd_app a on a.code = p.app_code
        where p.code = #{formCode}
          and a.code = #{appCode};
    </select>
</mapper>
