package com.sinitek.sirm.nocode.support.util;

import com.sinitek.sirm.nocode.app.service.IZdAppManagerService;
import com.sinitek.sirm.nocode.app.service.IZdAppService;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0416
 * @description 页面el表达式
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class AppRightEl {
    @Resource
    public IZdPageService pageService;
    @Resource
    public IZdPageAuthService pageAuthService;
    @Resource
    public IZdAppManagerService managerService;
    @Resource
    public IZdAppService appService;


    /**
     * 根据页面编码获取应用编码
     *
     * @param pageCode 页面编码
     * @return 应用编码
     */
    public String getAppCodeByPageCode(String pageCode) {
        return pageService.getAppCodeByCode(Collections.singletonList(pageCode));
    }

    public String getAppCodeByPageId(Long id) {
        return pageService.getAppCodeById(id);
    }

    /**
     * 根据页面编码获取应用编码
     *
     * @param pageCodeList 页面编码
     * @return 应用编码
     */
    public String getAppCodeByPageCode(List<String> pageCodeList) {
        return pageService.getAppCodeByCode(pageCodeList);
    }

    /**
     * 通过权限id 获取应用的code
     *
     * @param id 权限id
     * @return 应用编码
     */
    public String getAppCodeByPageAuthId(Long id) {
        return pageAuthService.getAppCodeByPageAuthId(id);
    }

    /**
     * 判断是否有应用权限
     *
     * @param pageCode 页面编码
     * @param orgId    组织id
     * @return 是否有权限
     */
    public boolean hasAppAuthByPageCode(String pageCode, String orgId) {
        String appCode = getAppCodeByPageCode(pageCode);
        return managerService.hasAuth(appCode, orgId);
    }

    /**
     * 获取应用是否启用
     *
     * @param pageCode 页面编码
     * @return 是否启用
     */
    public boolean isEnable(String pageCode) {
        return appService.isEnable(pageCode);
    }

}
