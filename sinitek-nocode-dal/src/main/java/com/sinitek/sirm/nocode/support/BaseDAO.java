package com.sinitek.sirm.nocode.support;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.common.mapper.CommonMapper;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0407
 * @description 基本DAO
 * @since 1.0.0-SNAPSHOT
 */
public class BaseDAO<M extends BaseMapper<T>, T, DAO extends ServiceImpl<M, T>> {
    @Autowired
    protected DAO dao;

    @Resource
    private CommonMapper commonMapper;


    protected LambdaUpdateWrapper<T> lu() {
        return Wrappers.lambdaUpdate(dao.getEntityClass());
    }

    protected List<T> list(Wrapper<T> queryWrapper) {
        return dao.list(queryWrapper);
    }


    protected M getBaseMapper() {
        return dao.getBaseMapper();
    }

    /**
     * 主键校验
     *
     * @param id 主键
     */
    protected T idCheck(Long id) {
        if (Objects.nonNull(id)) {
            T t = dao.getById(id);
            if (Objects.isNull(t)) {
                throw new BussinessException("3000008", id);
            }
            return t;
        }
        return null;
    }

    /**
     * eq or in 操作
     *
     * @param column 列
     * @param o      值
     * @return lambdaQuery
     */
    protected LambdaQueryWrapper<T> eqOrIn(SFunction<T, ?> column, Object o) {
        return LamWrapper.eqOrIn(column, o, Wrappers.lambdaQuery(dao.getEntityClass()));
    }

    /**
     * 判断是否存在
     *
     * @param c     列
     * @param value 值
     * @return 是否存在
     */
    protected <A> boolean exists(SFunction<A, ?> c, Object value) {
        LamWrapper<A> ins = LamWrapper.eqOrIn(c, value);
        return commonMapper.exists(ins);
    }

    /**
     * 判断是否存在
     *
     * @param query 查询
     * @return 是否存在
     */
    protected <A> boolean exists(LamWrapper<A> query) {
        return commonMapper.exists(query);
    }

    /**
     * 获取字符串值
     *
     * @param query 查询
     * @return 字符串值
     */
    protected <A> String stringValue(LamWrapper<A> query) {
        return commonMapper.stringValue(query);
    }
}
