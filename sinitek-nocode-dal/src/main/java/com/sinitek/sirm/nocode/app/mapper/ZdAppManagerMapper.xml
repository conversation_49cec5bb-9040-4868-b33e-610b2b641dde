<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinitek.sirm.nocode.app.mapper.ZdAppManagerMapper">


    <select id="hasAuth" resultType="java.lang.Boolean">
        select exists(select 1 from zd_app_manager where app_code = #{appCode} and org_id = #{orgId})
    </select>
</mapper>
