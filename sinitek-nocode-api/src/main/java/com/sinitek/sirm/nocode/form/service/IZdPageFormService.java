package com.sinitek.sirm.nocode.form.service;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormUpdateDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-12 11:04:49
 * @description 针对表【zd_page_form(页面表单表)】的数据库操作Service
 */
public interface IZdPageFormService {
    Boolean saveOrUpdate(ZdPageFormUpdateDTO pageForm);

    /**
     * 预览表单
     *
     * @param code 表单编码
     * @return 表单
     */
    ZdPageFormDTO view(String code);

    /**
     * 获取最新发布的表单
     *
     * @param code 表单编码
     * @return 表单
     */
    ZdPageFormDTO getPublishedForm(String code);

    /**
     * 获取最新发布的表单PageData
     *
     * @param code 表单编码
     * @return 表单
     */
    ZdPageFormDTO getFormPageData(String code);

    /**
     * 更新表单
     *
     * @param id 表单主键
     * @return 最新表单的id
     */
    ZdPageFormDTO updateVersion(Long id);

    /**
     * 发布表单
     *
     * @param code 表单code
     */
    void publish(String code);

    /**
     * 立即生效
     *
     * @param zdPageFormDTO 表单数据
     */
    void effectVersion(ZdPageFormDTO zdPageFormDTO);

    /**
     * 应用
     *
     * @param id 表单主键
     */
    ZdPageFormDTO applyHistoryVersion(Long id);

    /**
     * 版本历史
     *
     * @param id 表单主键
     * @return 版本历史
     */
    List<ZdPageFormDTO> versionHistory(Long id);

    /**
     * 表单更改历史
     *
     * @param id 表单主键
     * @return 更改历史
     */
    List<ZdPageFormHistoryDTO> updateHistory(Long id);

    /**
     * 表单编码是否属于某个应用
     *
     * @param formCode 表单编码
     * @param appCode  应用编码
     * @return 是否属于
     */

    boolean codeBelongsToAppCode(String formCode, String appCode);

    /**
     * 表单是否存在
     *
     * @param formCode 表单编码
     * @return 是否存在
     */
    boolean exists(String formCode);

    /**
     * 表单是否发布
     *
     * @param formCode 表单编码
     * @return 是否发布
     */
    boolean isPublished(String formCode);

}
