package com.sinitek.sirm.nocode.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0506
 * @since 1.0.0-SNAPSHOT
 */

@ApiModel(description = "获取应用token的结果")
@Data
public class ZdAppGetAccessTokenResponseBodyDTO {
    @ApiModelProperty("应用token")
    public String accessToken;
    @ApiModelProperty("应用token过期时间")
    public Long expireIn;
}
