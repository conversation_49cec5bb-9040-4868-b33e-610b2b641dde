package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.common.dto.ZdCurrentOrgIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0313
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "保存应用管理员DTO", value = "保存应用管理员DTO")
@Data
public class ZdAppManagerSaveDTO extends ZdCurrentOrgIdDTO {

    /**
     * 应用编码
     */
    @ApiModelProperty(value = "应用编码", example = "abc1000")
    private String appCode;
    /**
     * 人员id数组
     */
    @NotEmpty(message = "人员id数组不能为空")
    @ApiModelProperty("人员id数组")
    private List<String> orgIdList;


}
