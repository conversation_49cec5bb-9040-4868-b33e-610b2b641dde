package com.sinitek.sirm.nocode.common.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ClassUtils;

/**
 * <AUTHOR>
 * @version 2025.0328
 * @since 1.0.0-SNAPSHOT
 */
@SuppressWarnings({"unchecked"})
public class CopyUtil {
    private static final String[] IGNORE_PROPERTIES = {"id"};


    private CopyUtil() {
    }

    /**
     * 数据复制
     *
     * @param source           来源
     * @param target           目标实体
     * @param ignoreProperties 忽略的复制的属性
     */
    public static <T> T copyProperties(Object source, Object target, String... ignoreProperties) {
        // 假如是 class 的话，就转化为 实体
        if (target instanceof Class) {
            target = instantiateClass((Class<T>) target);
        }
        BeanUtils.copyProperties(source, target, ignoreProperties);
        return (T) target;
    }

    /**
     * 实体复制
     *
     * @param source 来源
     * @param target 目标实体
     */
    public static <T> T copyEntityProperties(Object source, Object target) {
        return copyProperties(source, target, IGNORE_PROPERTIES);
    }

    /**
     * 实例化数据
     *
     * @param t   class
     * @param <T> class 泛型
     * @return 实体
     */
    public static <T> T instantiateClass(Class<T> t) {
        return BeanUtils.instantiateClass(t);
    }

    /**
     * 复制属性，如果目标属性为空，则复制源属性
     *
     * @param source 源
     * @param target 目标
     */
    public static void copyPropertiesIfTargetPropertyValueIsNull(Object source, Object target) {
        Class<?> userClass = ClassUtils.getUserClass(source);
        Object newSource = instantiateClass(userClass);
        copyProperties(source, newSource);
        CopyOptions copyOptions = CopyOptions.create();
        // 忽略值为空的属性
        copyOptions.setIgnoreNullValue(true);
        // 将不为空的复制到新的实体中
        BeanUtil.copyProperties(target, newSource, copyOptions);
        // 再将结果
        BeanUtil.copyProperties(newSource, target);

    }
}
