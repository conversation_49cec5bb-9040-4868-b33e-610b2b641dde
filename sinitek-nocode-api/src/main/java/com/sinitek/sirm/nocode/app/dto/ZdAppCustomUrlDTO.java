package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/4/30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdAppCustomUrlDTO extends ZdIdDTO {
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", example = "我的简历")
    private String name;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", example = "jicode123456")
    private String url;

}
