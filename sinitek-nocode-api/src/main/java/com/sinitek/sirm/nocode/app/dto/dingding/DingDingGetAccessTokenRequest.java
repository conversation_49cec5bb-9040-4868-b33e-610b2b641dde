package com.sinitek.sirm.nocode.app.dto.dingding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0424
 * @description 钉钉参数
 * @since 1.0.0-SNAPSHOT
 */
@Data
public class DingDingGetAccessTokenRequest {
    @NotBlank(message = "Client ID不能为空")
    @ApiModelProperty(value = "Client ID", example = "dingaihzdfooeg2bjrhb", required = true)
    public String appKey;
    @NotBlank(message = "Client Secret不能为空")
    @ApiModelProperty(value = "Client Secret", example = "JRfHMrG6zNOlAusr39sjtRi6qrmor0C36ivlGS5Z4IGj0M5wjyyzBkhFx1D2_JQ9X", required = true)
    public String appSecret;
}
