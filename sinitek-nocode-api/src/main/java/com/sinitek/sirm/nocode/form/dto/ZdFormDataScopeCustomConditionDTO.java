package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.form.enumerate.LogicOperatorEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0326
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "数据范围自定义过滤条件")
@Data
public class ZdFormDataScopeCustomConditionDTO {
    @ApiModelProperty("操作类型，例如：AND,OR")
    private LogicOperatorEnum logicOperator;
    @ApiModelProperty("搜索字段")
    private List<ZdFormDataSearchFieldDTO> conditions;
}
