package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.form.dto.ZdFormButtonsSupportDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSaveDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSearchParamDTO;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.org.entity.Employee;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2025-03-12 13:38:21
 * @description 针对表【zd_page_auth(页面权限表)】的数据库操作Service
 */
public interface IZdPageAuthService {
    /**
     * 获取当前登陆人的权限
     *
     * @param pageCode 页面编码
     * @param authType 权限类型
     * @param orgId    当前登陆人id
     * @return 权限
     * <AUTHOR>
     */
    ZdPageAuthDTO rightQuery(String pageCode, PageAuthTypeEnum authType, String orgId);

    /**
     * 获取当前登陆人的页面按钮权限
     *
     * @param pageCode 页面编码
     * @param orgId    当前登陆人id
     * @return 页面按钮权限
     */
    ZdFormButtonsSupportDTO getFormButtonsSupport(String pageCode, String orgId);


    /**
     * 通过页面编码和权限类型获取权限列表
     *
     * @param pageCode 页面编码
     * @param authType 权限类型
     * @return 权限列表
     */
    List<ZdPageAuthDTO> list(String pageCode, PageAuthTypeEnum authType);


    /**
     * 通过权限列表获取权限组织id
     *
     * @param list 权限列表
     * @return 人员orgId
     */
    Set<String> findAuthOrgIds(List<ZdPageAuthDTO> list);

    List<Employee> findAuthEmp(List<ZdPageAuthDTO> list);

    /**
     * 通过页面编码获取可提交人员信息
     *
     * @param pageCode 页面编码
     * @return 人员信息
     */
    List<Employee> findSubmitEmp(String pageCode);


    /**
     * 分页查询权限列表
     *
     * @param param 查询参数
     * @return 权限列表
     */

    TableResult<ZdPageAuthSaveDTO> search(ZdPageAuthSearchParamDTO param);

    /**
     * 权限复制
     *
     * @param id 权限
     * @return 是否复制成功
     */
    Long copyPageAuth(Long id);

    Long saveOrUpdate(ZdPageAuthSaveDTO pageAuthDTO);

    /**
     * 通过权限id 获取应用的code
     *
     * @param id 权限id
     * @return 应用编码
     */
    String getAppCodeByPageAuthId(Long id);

    /**
     * 删除权限
     *
     * @param id 权限id
     * @return 是否删除成功
     */
    boolean removeById(Long id);


}
