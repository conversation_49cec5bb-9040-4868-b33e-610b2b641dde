package com.sinitek.sirm.nocode.form.enumerate;

import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 2025.0527
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "值的类型枚举")
@Getter
public enum ValueTypeEnum implements BaseStringEnum {
    STRING("text", "字符串类型", "text", null),
    NUMBER("int", "数值类型", "numeric", null),
    BOOLEAN("boolean", "布尔类型", "boolean", Boolean.class),
    DATE("date", "日期类型(只有年月日)", "date", Date.class),
    TIME("time", "时间类型(时分秒)", "time", Date.class),
    TIMESTAMP("timestamp", "日期时间类型(年月日时分秒)", "timestamp", Date.class),
    ;

    @JsonValue
    @ApiModelProperty("值")
    private final String value;
    @ApiModelProperty("中文名称")
    private final String label;
    @ApiModelProperty("pg数据库里面的类型")
    private final String pgType;

    private final Class<?> rawType;

    ValueTypeEnum(String value, String label, String pgType, Class<?> rawType) {
        this.value = value;
        this.label = label;
        this.pgType = pgType;
        this.rawType = rawType;
    }

    /**
     * 添加pg类型
     *
     * @param columnName 列名
     * @return 列名
     */
    public String addPgType(String columnName) {
        return "(" + columnName + ")::" + pgType;
    }
}
