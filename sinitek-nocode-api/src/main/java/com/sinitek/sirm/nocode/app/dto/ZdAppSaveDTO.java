package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.common.dto.ZdCurrentOrgIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0311
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "应用保存DTO")
public class ZdAppSaveDTO extends ZdCurrentOrgIdDTO {
    @ApiModelProperty(value = "应用名称", required = true, example = "报销系统")
    @NotBlank(message = "应用名称不能为空")
    @Length(max = 100, message = "应用名称不能超过100个字符")
    private String name;
}
