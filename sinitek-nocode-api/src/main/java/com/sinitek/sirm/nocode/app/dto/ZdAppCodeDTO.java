package com.sinitek.sirm.nocode.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 2025.0422
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "应用编码DTO")
@Data
public class ZdAppCodeDTO {
    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空")
    @ApiModelProperty(value = "应用编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
    private String appCode;
}
