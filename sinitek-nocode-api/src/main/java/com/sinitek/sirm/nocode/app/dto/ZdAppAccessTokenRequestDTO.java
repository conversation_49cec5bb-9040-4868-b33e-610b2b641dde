package com.sinitek.sirm.nocode.app.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @version 2025.0506
 * @since 1.0.0-SNAPSHOT
 */

@ApiModel(description = "获取应用tokeDTO")
@Data
public class ZdAppAccessTokenRequestDTO {
    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空")
    @Pattern(regexp = "^[a-zA-Z][\\w-]*$", message = "应用编码必须以字母开头，由英文字母、数字、下划线（_）、连字符（-）组成")
    @ApiModelProperty(name = "code", value = "应用编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
    private String code;


    /**
     * 应用密钥
     */
    @NotBlank(message = "应用密钥不能为空")
    @ApiModelProperty("应用密钥")
    private String appSecret;
}
