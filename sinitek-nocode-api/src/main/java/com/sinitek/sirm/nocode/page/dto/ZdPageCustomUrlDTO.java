package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZdPageCustomUrlDTO extends ZdIdDTO {
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称",example = "我的简历")
    private String name;

    /**
     * 自定义的页面地址
     */
    @ApiModelProperty(value = "自定义的页面地址",example = "jicode123456")
    private String url;

    /**
     * 页面编码
     */
    @ApiModelProperty(value = "页面编码", example = "page_123456")
    private String code;
}
