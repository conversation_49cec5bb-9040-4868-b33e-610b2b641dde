package com.sinitek.sirm.nocode.form.dto;

import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.ValueTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.VariableTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "表单数据搜索字段参数")
@Data
public class ZdFormDataSearchFieldDTO {
    @NotBlank(message = "数据库字段不能为空")
    @ApiModelProperty(value = "唯一标志，数据库字段。说明，当查询子表单时(ZDChildForm_0b09)，假如要查询的字段为：ZDSelectMultiple_2odb，那么key的值为：ZDChildForm_0b09.ZDSelectMultiple_2odb", required = true)
    private String key;
    /**
     * 值类型
     */
    @ApiEnumProperty(required = true)
    private ValueTypeEnum valueType;
    /**
     * 变量类型
     */
    @ApiEnumProperty
    private VariableTypeEnum variableType;
    /**
     * 操作符
     */
    @NotNull(message = "操作符不能为空")
    @ApiEnumProperty(required = true)
    private OperatorEnum operator;

    /**
     * 组件类型
     */
    @ApiEnumProperty(required = true)
    private PageDataComponentTypeEnum componentName;
    /**
     * 值
     */
    @ApiModelProperty(value = "值", required = true)
    private Object value;
}
