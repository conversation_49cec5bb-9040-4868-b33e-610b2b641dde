package com.sinitek.sirm.nocode.form.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.page.dto.PageDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0604
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "页面表单结构")
@Data
public class ZdFormPageDataDTO {
    @ApiModelProperty(value = "组件的唯一标识", example = "ZDInput_dpk8")
    private String ref;
    @ApiModelProperty(value = "组件类型", example = "ZDSelectMultiple")
    private String componentName;
    @ApiModelProperty(value = "组件的属性", example = "{\"label\": \"下拉框-多选\"}")
    private Props props;
    @ApiModelProperty(value = "子类组件")
    private List<ZdFormPageDataDTO> children;


    @Data
    public static class Props {
        @ApiModelProperty(value = "组件的名称", example = "名字")
        private String label;
    }

    /**
     * 查找所有的表单组件
     *
     * @return 表单组件数据
     */
    public List<PageDataDTO> findZdForm(Predicate<ZdFormPageDataDTO> test) {
        List<ZdFormPageDataDTO> zdForm = findTargetChild(PageDataComponentTypeEnum.ZD_FORM.getValue());
        if (Objects.nonNull(test)) {
            zdForm = zdForm.stream().filter(test).collect(Collectors.toList());
        }
        return zdForm.stream().map(child -> {
            PageDataDTO pageDataDTO = new PageDataDTO();
            pageDataDTO.setRef(child.getRef());
            pageDataDTO.setComponentName(child.getComponentName());
            pageDataDTO.setLabel(child.getProps().getLabel());
            return pageDataDTO;
        }).collect(Collectors.toList());
    }


    /**
     * 递归查找指定类型的子节点
     *
     * @param type 组件类型
     * @return 子节点
     */
    private List<ZdFormPageDataDTO> findTargetChild(String type) {
        return findTargetChild(a -> Objects.equals(a.getComponentName(), type));
    }

    /**
     * 递归查找指定类型的子节点
     *
     * @param test 符合要求的测试
     * @return 子节点
     */
    private List<ZdFormPageDataDTO> findTargetChild(Predicate<ZdFormPageDataDTO> test) {
        List<ZdFormPageDataDTO> list = new ArrayList<>();
        if (test.test(this)) {
            if (Objects.isNull(children)) {
                return list;
            }
            return children;
        } else if (CollectionUtil.isNotEmpty(children)) {
            children.forEach(child -> list.addAll(child.findTargetChild(test)));
        }
        return list;
    }
}
