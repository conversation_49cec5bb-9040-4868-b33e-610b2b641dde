package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseIntegerEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 页面类型
 *
 * <AUTHOR>
 * @version 2025.0327
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "页面类型枚举")
@Getter
public enum PageTypeEnum implements BaseIntegerEnum {
    NORMAL_FORM(0, "普通表单", true),
    WORKFLOW_FORM(1, "流程表单", true),
    REPORT_PAGE(2, "报表页面", false),
    GROUP_PAGE(9, "分组目录", false),
    ;

    /**
     * 值
     */
    @JsonValue
    private final Integer value;
    /**
     * 名称
     */
    private final String label;

    private final boolean enable;

    PageTypeEnum(Integer value, String label, boolean enable) {
        this.value = value;
        this.label = label;
        this.enable = enable;
    }


    @JsonCreator
    public static PageTypeEnum fromValue(Integer value) {
        return Arrays.stream(PageTypeEnum.values()).filter(a -> Objects.equals(value, a.getValue())).findAny().orElse(null);
    }

    public static boolean isForm(PageTypeEnum pageTypeEnum) {
        return Objects.equals(pageTypeEnum, NORMAL_FORM) || Objects.equals(pageTypeEnum, WORKFLOW_FORM);
    }
}
