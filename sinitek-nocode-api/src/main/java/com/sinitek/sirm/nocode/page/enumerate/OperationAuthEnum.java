package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "操作权限枚举")
@Getter
public enum OperationAuthEnum implements BaseStringEnum {
    VIEW("0", "查看", PageAuthTypeEnum.DATA_AUTH.getValue()),
    EDIT("1", "编辑", PageAuthTypeEnum.DATA_AUTH.getValue()),
    DELETE("2", "删除", PageAuthTypeEnum.DATA_AUTH.getValue()),
    CHANGE_RECORD("3", "变更记录", PageAuthTypeEnum.DATA_AUTH.getValue()),
    COMMENT("4", "评论", PageAuthTypeEnum.DATA_AUTH.getValue()),
    PRINT("5", "打印", PageAuthTypeEnum.DATA_AUTH.getValue()),
    SUBMIT("8", "提交", PageAuthTypeEnum.SUBMIT_AUTH.getValue()),
    TEMPORARY_SAVE("9", "暂存", PageAuthTypeEnum.SUBMIT_AUTH.getValue());

    /**
     * 值
     */
    @JsonValue
    private final String value;
    /**
     * 名称
     */
    private final String label;
    /**
     * 类型
     */
    private final int type;

    OperationAuthEnum(String value, String label, int type) {
        this.value = value;
        this.label = label;
        this.type = type;
    }


    @JsonCreator
    public static OperationAuthEnum fromValue(String value) {
        return MAP.get(value);
    }

    /**
     * 从字符串变成枚举
     *
     * @param list 值列表
     * @return 枚举
     */
    public static List<OperationAuthEnum> fromListValue(List<String> list) {
        return list.stream().map(OperationAuthEnum::fromValue).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取所有值
     *
     * @return 所有的值，用逗号隔开
     */
    public static String allValue() {
        return Arrays.stream(OperationAuthEnum.values()).map(OperationAuthEnum::getValue).collect(Collectors.joining(","));
    }

    /**
     * 获取所有数据权限的值
     *
     * @return 所有数据权限的值
     */
    public static String allDataValue() {
        return Arrays.stream(OperationAuthEnum.values()).filter(a -> a.getType() == PageAuthTypeEnum.DATA_AUTH.getValue()).map(OperationAuthEnum::getValue).collect(Collectors.joining(","));
    }


    private static final Map<String, OperationAuthEnum> MAP = BaseEnum.map(OperationAuthEnum.class);
}
