package com.sinitek.sirm.nocode.app.dto;

import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025.0618
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "应用的基本信息")
@Data
public class ZdAppBaseInfoDTO {
    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", example = "销假应用")
    private String name;


    /**
     * 应用背景
     */
    @ApiModelProperty("应用背景")
    private String background;

    /**
     * 主题颜色
     */
    @ApiModelProperty("主题颜色")
    private String themeColor;

    /**
     * 状态，0:未启用，1:已经启用
     */
    @ApiModelProperty("状态，0:未启用，1:已经启用")
    private StatusEnum status;


    /**
     * 是否是应用管理员
     */
    @ApiModelProperty("是否是应用管理员，true Or false")
    private Boolean adminFlag;
}
