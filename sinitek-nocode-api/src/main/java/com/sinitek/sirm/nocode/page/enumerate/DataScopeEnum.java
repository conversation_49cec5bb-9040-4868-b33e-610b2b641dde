package com.sinitek.sirm.nocode.page.enumerate;

import com.fasterxml.jackson.annotation.JsonValue;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseStringEnum;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 数据范围枚举
 *
 * <AUTHOR>
 * @version 2025.0324
 * @since 1.0.0-SNAPSHOT
 */
@ApiModel(description = "数据范围枚举")
@Getter
public enum DataScopeEnum implements BaseStringEnum {
    ALL("0", "全部数据"),
    SELF("1", "本人提交"),
    SELF_DEPARTMENT("2", "本部门提交"),
    SAME_DEPARTMENT("3", "同级部门提交"),
    SUB_DEPARTMENT("4", "下级部门提交"),
    CUSTOM_DEPARTMENT("5", "自定义部门提交"),
    CUSTOM_FILTER("6", "自定义过滤条件");

    /**
     * 值
     */
    @JsonValue
    private final String value;
    /**
     * 名称
     */
    private final String label;


    DataScopeEnum(String value, String label) {
        this.value = value;
        this.label = label;

    }

    /**
     * 根据字符串获取枚举
     *
     * @param str 字符串
     * @return 枚举数组
     */
    public static List<DataScopeEnum> fromStr(String str) {
        if (Objects.isNull(str)) {
            return new ArrayList<>();
        }
        List<DataScopeEnum> list = new ArrayList<>();
        for (String s : str.split(",")) {
            DataScopeEnum dataScopeEnum = MAP.get(s);
            if (dataScopeEnum != null) {
                list.add(dataScopeEnum);
            }
        }
        return list;
    }

    private static final Map<String, DataScopeEnum> MAP = BaseEnum.map(DataScopeEnum.class);


}
