package com.sinitek.sirm.nocode.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.nocode.app.dto.ZdAppAccessTokenRequestDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppBaseInfoDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppCustomUrlDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppGetAccessTokenResponseBodyDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSaveDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSecretDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSettingDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppUpdateNameDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025-03-11 09:32:06
 * @description 针对表【zd_app(应用表)】的数据库操作Service
 */
public interface IZdAppService {

    ZdAppDTO getById(Long id);

    IPage<ZdAppDTO> search(ZdAppSearchParamDTO param);

    /**
     * 创建一个应用
     *
     * @param saveDTO 保存时的参数
     * @return 应用id
     */
    String create(ZdAppSaveDTO saveDTO);

    /**
     * 修改应用名称
     *
     * @param updateNameDTO 修改应用名称参数
     * @return 是否修改成功
     */
    Boolean updateName(ZdAppUpdateNameDTO updateNameDTO);

    /**
     * 修改应用
     *
     * @param zdAppDTO 应用
     * @return 是否成
     */
    Boolean update(ZdAppDTO zdAppDTO);

    /**
     * 根据编码获取应用
     *
     * @param appCode 应用编码
     * @return 应用
     */
    ZdAppDTO getByCode(String appCode);

    /**
     * 判断应用是否存在
     *
     * @param appCode 应用编码
     * @return 是否存在
     */
    boolean exists(String appCode);

    /**
     * 根据编码删除应用
     *
     * @param appCode 应用编码
     * @return 是否删除成功
     */
    Boolean deleteByCode(String appCode);

    /**
     * 修改应用状态
     *
     * @param appCode 应用编码
     * @return 是否修改成功
     */
    Boolean updateStatus(String appCode);

    /**
     * 查看应用密钥
     *
     * @param appCode 应用编码
     * @return 是否查看成功
     */
    ZdAppSecretDTO viewSecret(String appCode);

    /**
     * 应用设置
     *
     * @param appCode 应用编码
     * @return 应用设置
     */
    ZdAppSettingDTO setting(String appCode);

    /**
     * 保存访问地址url
     *
     * @param appCustomUrlDTOList 保存访问地址url
     * @param orgId               人员id
     */
    void savePageCustomUrl(List<ZdAppCustomUrlDTO> appCustomUrlDTOList, String orgId);


    /**
     * 获取应用token
     *
     * @param appAccessTokenRequestDTO 请求参数
     * @return token
     */
    ZdAppGetAccessTokenResponseBodyDTO accessToken(ZdAppAccessTokenRequestDTO appAccessTokenRequestDTO);

    /**
     * 应用是否启用
     *
     * @param appCode 应用编码
     * @return 是否启用成功
     */
    boolean isEnable(String appCode);

    /**
     * 获取应用基本信息
     *
     * @param appCode      应用编码
     * @param currentOrgId 当前组织id
     * @return 应用基本信息
     */

    ZdAppBaseInfoDTO getBaseInfo(String appCode, String currentOrgId);
}
