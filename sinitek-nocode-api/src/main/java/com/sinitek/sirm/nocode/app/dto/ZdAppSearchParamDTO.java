package com.sinitek.sirm.nocode.app.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.dto.ZdSearchParamDTO;
import com.sinitek.sirm.nocode.common.enumerate.StatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0401
 * @description 应用查询查询条件
 * @since 1.0.0-SNAPSHOT
 */
@Data
@ApiModel(description = "应用查询查询条件")
@EqualsAndHashCode(callSuper = true)
public class ZdAppSearchParamDTO extends ZdSearchParamDTO {
    @ApiModelProperty(value = "应用状态", example = "1")
    private StatusEnum status;

    @ApiModelProperty(value = "是不是我创建的", example = "true")
    private Boolean myCreate;

    /**
     * 不是由前台传递
     */
    @JsonIgnore
    @ApiModelProperty(value = "当前登陆人orgId", example = "9990091")
    private String orgId;
    /**
     * 不是由前台传递
     */
    @JsonIgnore
    @ApiModelProperty(value = "主键查询", example = "1914879030746812418")
    private Long id;


}
