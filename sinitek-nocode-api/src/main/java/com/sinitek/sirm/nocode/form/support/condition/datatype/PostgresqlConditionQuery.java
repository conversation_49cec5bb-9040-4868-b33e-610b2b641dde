package com.sinitek.sirm.nocode.form.support.condition.datatype;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataSearchFieldDTO;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.ValueTypeEnum;
import com.sinitek.sirm.nocode.form.support.condition.ConditionQuery;
import com.sinitek.sirm.nocode.form.support.condition.OperationInterface;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * postgresql 条件查询
 *
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class PostgresqlConditionQuery implements ConditionQuery {

    private final Map<OperatorEnum, OperationInterface> OPERATIONS = getOperationMap();

    @Override
    public void applyCustomFieldCondition(QueryWrapper<?> queryBuilder, ZdFormDataSearchFieldDTO condition) {
        String key = condition.getKey();
        // 是否使用exist 操作
        boolean exist = false;
        // 列名
        String columnName = key;
        if (key.contains(".")) {
            columnName = makeChildColumnNameName(key);
            exist = true;
        }
        // 处理数组类型的特殊情况
        if (condition.getValue() instanceof List && isArrayField(condition)) {
            columnName = columnName + ZdPgSqlConstant.ARRAY_ALL;
            exist = true;
        }
        if (!exist) {
            columnName = buildColumnName(key, condition.getValueType());
        }
        Class<?> fieldType = Optional.ofNullable(condition.getValueType())
                .map(ValueTypeEnum::getRawType)
                .orElse(null);
        OperatorEnum operator = condition.getOperator();
        Object value = condition.getValue();
        if (exist) {
            OperationInterface operationInterface = OPERATIONS.get(condition.getOperator());
            if (Objects.nonNull(operationInterface)) {
                value = operator.getObject(fieldType, value);
                operationInterface.apply(queryBuilder, columnName, value);
            }

        } else {
            condition.getOperator().apply(fieldType, queryBuilder, columnName, condition.getValue());
        }
    }

    @Override
    public DbType dbType() {
        return DbType.POSTGRE_SQL;
    }


    /**
     * 判断是否为数组字段
     *
     * @param condition 查询条件
     * @return 是否为数组字段
     */
    private static boolean isArrayField(ZdFormDataSearchFieldDTO condition) {
        PageDataComponentTypeEnum componentType = Optional.ofNullable(condition.getComponentName())
                .orElseGet(() -> PageDataComponentTypeEnum.formKey(condition.getKey()));

        return Optional.ofNullable(componentType)
                .map(PageDataComponentTypeEnum::getFieldType)
                .map(List.class::equals)
                .orElse(false);
    }


    /**
     * 构建列名
     *
     * @param key       字段键
     * @param valueType 值类型
     * @return 构建后的列名
     */
    private static String buildColumnName(String key, ValueTypeEnum valueType) {
        String columnName = buildStringColumnName(key);
        return Optional.ofNullable(valueType)
                .map(type -> type.addPgType(columnName))
                .orElse(columnName);
    }

    /**
     * 构建字符串列名
     *
     * @param key 字段键
     * @return 字符串形式的列名
     */
    private static String buildStringColumnName(String key) {
        return String.format(FormConstant.FORM_DATA_KEY_TEXT, key);
    }


    /**
     * 构建子表单列名
     *
     * @param key 字段键
     * @return 构建后的列名
     */
    private static String makeChildColumnNameName(String key) {
        String[] split = key.split("\\.");
        int length = split.length;
        int father = length - 1;
        List<String> list = new ArrayList<>();
        for (int i = 0; i < father; i++) {
            list.add(split[i] + ZdPgSqlConstant.ARRAY_ALL);
        }
        list.add(split[father]);
        return String.join(".", list);
    }
}
