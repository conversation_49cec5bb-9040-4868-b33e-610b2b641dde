package com.sinitek.sirm.nocode.form.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import com.sinitek.sirm.nocode.common.support.handler.JsonbTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 页面表单历史表
 *
 * @TableName zd_page_form_history
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "历史表单DTO")
public class ZdPageFormHistoryDTO extends ZdIdDTO {
    /**
     * 表单配置
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    @ApiModelProperty(value = "表单配置")
    private String pageData;

    /**
     * 页面表单id
     */
    @ApiModelProperty(value = "页面表单id")
    private Long pageFormId;
}
