<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.sinitek.sirm</groupId>
    <artifactId>sinitek-nocode</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>com.sinitek.sirm</groupId>
  <artifactId>sinitek-nocode-service-design</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <skip.jar>false</skip.jar>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.sinitek.sirm</groupId>
      <artifactId>sinitek-nocode-dal</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sinitek.sinicube</groupId>
      <artifactId>sinitek_commoncore</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sinitek.sinicube</groupId>
      <artifactId>sinitek-cloud-base</artifactId>
    </dependency>
    <dependency>
      <groupId>com.sinitek.sinicube</groupId>
      <artifactId>sinitek-cloud-sirmapp-sdk</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>
  </dependencies>
</project>
