package com.sinitek.sirm.nocode.app.controller;

import com.sinitek.sirm.common.limit.annotation.SiniCubeLimiter;
import com.sinitek.sirm.common.limit.enumerate.SiniCubeLimiterMode;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.app.dto.ZdAppAccessTokenRequestDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppGetAccessTokenResponseBodyDTO;
import com.sinitek.sirm.nocode.app.service.IZdAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0608
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/openness-api/nocode/app", tags = "应用对外接口")
@RequestMapping("/frontend/api/openness-api/nocode/app")
public class ZdApplicationOpenApiController {

    @Resource
    private IZdAppService zdAppService;

    /**
     * 获取应用的token
     * <ol>
     *     <li>应用编码和密钥必须正确</li>
     *     <li>频率限制，同一个ip, 一分钟两次，否则提示：接口调用太频繁，请稍后再试</li>
     * </ol>
     *
     * @param zdAppAccessTokenRequestDTO 请求参数
     * @return token
     */
    @SiniCubeLimiter(
            mode = SiniCubeLimiterMode.SERVER,
            rate = "${nocode.app.token.limit.rate:2}",
            interval = "${nocode.app.token.limit.interval:60}",
            blocking = false
    )
    @ApiOperation(value = "获取应用的token", notes = "需要传递应用的编码和密钥，查看密钥和编码需要管理员权限")
    @PostMapping("/access-token")
    public RequestResult<ZdAppGetAccessTokenResponseBodyDTO> accessToken(
            @ApiParam(name = "获应用toke参数", value = "获应用toke参数")
            @RequestBody ZdAppAccessTokenRequestDTO zdAppAccessTokenRequestDTO
    ) {
        ZdAppGetAccessTokenResponseBodyDTO zdAppGetAccessTokenResponseBodyDTO = zdAppService.accessToken(zdAppAccessTokenRequestDTO);
        if (Objects.isNull(zdAppGetAccessTokenResponseBodyDTO)) {
            return RequestResult.fail("3000007");
        }
        return new RequestResult<>(zdAppGetAccessTokenResponseBodyDTO);
    }
}
