package com.sinitek.sirm.nocode.common.util;

import com.sinitek.sirm.nocode.common.constant.CacheKeyConstant;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0422
 * @description 组织工具类
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class OrgUtil {
    @Resource
    private IOrgService orgService;


    @Cacheable(value = CacheKeyConstant.ROLE_CACHE_KEY, key = "#roleId")
    public List<String> getOrgIdsByRoleId(String roleId) {
        if (StringUtils.isNotBlank(roleId) && !Objects.equals(roleId, "0")) {
            return orgService.findEmployeeInserviceByRoleId(roleId).stream().map(Employee::getOrigId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }
}
