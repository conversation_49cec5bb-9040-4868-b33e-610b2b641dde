package com.sinitek.sirm.nocode.page.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.sirm.common.dto.OptionDTO;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import com.sinitek.sirm.nocode.page.dao.ZdPageSceneDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageScene;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.SceneTypeEnum;
import com.sinitek.sirm.nocode.page.mapper.ZdPageSceneMapper;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageSceneService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025-03-12 14:21:01
 * @description 针对表【zd_page_scene(页面场景设置)】的数据库操作Service实现
 */
@Service
@RequiredArgsConstructor
public class ZdPageSceneServiceImpl extends BaseDAO<ZdPageSceneMapper, ZdPageScene, ZdPageSceneDAO>
        implements IZdPageSceneService {
    private final IZdPageAuthService authService;


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> pageCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdPageScene> queryWrapper = pageCodeQuery(pageCodeList);
        // 删除页面场景
        dao.remove(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveOrUpdate(ZdPageSceneDTO pageSceneSaveDTO) {
        Long id = pageSceneSaveDTO.getId();
        idCheck(id);
        String pageCode = pageSceneSaveDTO.getPageCode();
        ZdPageScene zdPageScene = dao.getOne(pageCodeQuery(Collections.singletonList(pageCode)));
        if (Objects.isNull(zdPageScene)) {
            zdPageScene = new ZdPageScene();
        }
        // 复制
        BeanUtils.copyProperties(pageSceneSaveDTO, zdPageScene);
        return dao.saveOrUpdate(zdPageScene);

    }

    @Override
    public ZdPageSceneDTO getPageScene(String pageCode) {
        return dao.getOne(pageCodeQuery(Collections.singletonList(pageCode)));
    }

    @Override
    public List<OptionDTO<Integer>> sceneTypeList(String pageCode) {
        List<ZdPageAuthDTO> list = authService.list(pageCode, PageAuthTypeEnum.DATA_AUTH);
        // 只有那些有范围的才显示
        List<ZdPageAuthDTO> authDTOList = list.stream().filter(auth -> StringUtils.isNotBlank(auth.getMemberOrgIds())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(authDTOList)) {
            return BaseEnum.option(SceneTypeEnum.class);
        }
        List<OptionDTO<Integer>> option = new ArrayList<>();
        OptionDTO<Integer> optionDTO = new OptionDTO<>();
        optionDTO.setName(SceneTypeEnum.COMMON.getLabel());
        optionDTO.setValue(SceneTypeEnum.COMMON.getValue());
        option.add(optionDTO);
        return option;
    }


    private LambdaQueryWrapper<ZdPageScene> pageCodeQuery(List<String> pageCodeList) {

        return eqOrIn(ZdPageScene::getPageCode, pageCodeList);
    }
}




