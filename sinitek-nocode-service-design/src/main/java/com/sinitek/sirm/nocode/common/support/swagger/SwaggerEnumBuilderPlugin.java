package com.sinitek.sirm.nocode.common.support.swagger;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.classmate.ResolvedType;
import com.fasterxml.classmate.TypeResolver;
import com.fasterxml.jackson.databind.introspect.AnnotatedField;
import com.fasterxml.jackson.databind.introspect.BeanPropertyDefinition;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.support.enumeratebase.BaseEnum;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.core.annotation.AnnotationUtils;
import springfox.documentation.service.AllowableListValues;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.schema.ModelPropertyBuilderPlugin;
import springfox.documentation.spi.schema.contexts.ModelPropertyContext;
import springfox.documentation.spi.service.ParameterBuilderPlugin;
import springfox.documentation.spi.service.contexts.ParameterContext;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 枚举swagger描述
 *
 * <AUTHOR>
 * @version 2025.0530
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class SwaggerEnumBuilderPlugin implements ModelPropertyBuilderPlugin, ParameterBuilderPlugin {

    @Resource
    private TypeResolver typeResolver;

    @Override
    public void apply(ModelPropertyContext modelPropertyContext) {
        try {
            ApiEnumProperty apiEnumProperty = extractApiEnumProperty(modelPropertyContext);
            if (apiEnumProperty == null) {
                return;
            }

            Class<?> rawType = getRawTypeFromModelProperty(modelPropertyContext);
            if (rawType == null) {
                return;
            }

            Result result = buildEnumResult(rawType, apiEnumProperty);
            if (result == null) {
                return;
            }

            applyToModelPropertyBuilder(modelPropertyContext, apiEnumProperty, result);
        } catch (Exception e) {
            log.error("重建swagger模型属性描述失败", e);
        }
    }

    /**
     * 从模型属性上下文中提取ApiEnumProperty注解
     */
    private ApiEnumProperty extractApiEnumProperty(ModelPropertyContext modelPropertyContext) {
        if (!modelPropertyContext.getBeanPropertyDefinition().isPresent()) {
            return null;
        }

        BeanPropertyDefinition beanPropertyDefinition = modelPropertyContext.getBeanPropertyDefinition().get();
        AnnotatedField field = beanPropertyDefinition.getField();

        return field != null ? field.getAnnotation(ApiEnumProperty.class) : null;
    }

    /**
     * 从模型属性上下文中获取原始类型
     */
    private Class<?> getRawTypeFromModelProperty(ModelPropertyContext modelPropertyContext) {
        return modelPropertyContext.getBeanPropertyDefinition()
                .map(BeanPropertyDefinition::getField)
                .map(AnnotatedField::getRawType)
                .orElse(null);
    }

    /**
     * 将结果应用到模型属性构建器
     */
    private void applyToModelPropertyBuilder(ModelPropertyContext modelPropertyContext,
                                             ApiEnumProperty apiEnumProperty,
                                             Result result) {
        modelPropertyContext.getBuilder()
                .description(result.description)
                .required(apiEnumProperty.required())
                .type(result.classType != null ? typeResolver.resolve(result.classType) : null)
                .example(StringUtils.isNotBlank(apiEnumProperty.example()) ? (Object) apiEnumProperty.example() : null)
                .name(StringUtils.isNotBlank(apiEnumProperty.name()) ? apiEnumProperty.name() : null)
                .allowableValues(result.allowableListValues);
    }

    /**
     * 根据原始类型和注解构建枚举结果
     */
    private static Result buildEnumResult(Class<?> rawType, ApiEnumProperty apiEnumProperty) {
        List<? extends BaseEnum<?>> enumList = getEnumList(rawType, apiEnumProperty);
        if (CollectionUtils.isEmpty(enumList)) {
            return null;
        }

        EnumProcessingResult processingResult = processEnumList(enumList);
        String description = buildDescription(apiEnumProperty, processingResult.descList, getEnumClazz(rawType, apiEnumProperty));

        Class<?> finalClassType = apiEnumProperty.keepDataType() ? null : processingResult.valueClass;

        return new Result(finalClassType, processingResult.allowableListValues, description);
    }

    /**
     * 根据原始类型和注解获取枚举列表
     */
    @SuppressWarnings({"unchecked"})
    private static <T extends Serializable, C extends BaseEnum<T>> List<? extends BaseEnum<?>> getEnumList(Class<?> rawType, ApiEnumProperty apiEnumProperty) {
        Class<?> enumClazz = getEnumClazz(rawType, apiEnumProperty);

        if (enumClazz != null && BaseEnum.class.isAssignableFrom(enumClazz)) {
            return BaseEnum.list((Class<C>) enumClazz, apiEnumProperty.type());
        }

        // 处理自定义枚举制造器
        Class<? extends SwaggerEnumMaker> customEnum = apiEnumProperty.customEnum();
        SwaggerEnumMaker swaggerEnumMaker = BeanUtils.instantiateClass(customEnum);
        return swaggerEnumMaker.findEnums(apiEnumProperty);
    }

    /**
     * 处理枚举列表以提取值和描述
     */
    private static EnumProcessingResult processEnumList(List<? extends BaseEnum<?>> enumList) {
        Class<?> valueClass = null;
        List<String> values = new ArrayList<>();
        List<String> descList = new ArrayList<>();

        for (BaseEnum<?> item : enumList) {
            Serializable value = item.getValue();
            if (value != null) {
                valueClass = value.getClass();
                values.add(value.toString());
            } else {
                values.add(null);
            }

            String desc = item.getDesc();
            String oneDesc = item.getValue() + ":" + item.getLabel() +
                    (StringUtils.isNotBlank(desc) ? "(" + desc + ")" : "");
            descList.add(oneDesc);
        }

        AllowableListValues allowableListValues = new AllowableListValues(values, "LIST");
        return new EnumProcessingResult(valueClass, allowableListValues, descList);
    }

    /**
     * 根据注解和枚举描述构建描述信息
     */
    private static String buildDescription(ApiEnumProperty apiEnumProperty,
                                           List<String> descList,
                                           Class<?> enumClazz) {
        StringBuilder description = new StringBuilder();

        // 添加基础描述
        if (StringUtils.isNotBlank(apiEnumProperty.description())) {
            description.append(apiEnumProperty.description());
        }

        // 添加API模型描述
        if (apiEnumProperty.apiModel()) {
            ApiModel apiModel = AnnotationUtils.findAnnotation(apiEnumProperty.enumClazz(), ApiModel.class);
            if (apiModel != null && StringUtils.isNotBlank(apiModel.description())) {
                appendToDescription(description, apiModel.description());
            }
        }

        // 添加枚举引用
        if (apiEnumProperty.viewEnum() && enumClazz != null) {
            String enumClazzName = com.baomidou.mybatisplus.core.toolkit.StringUtils
                    .firstToLowerCase(enumClazz.getSimpleName());
            appendToDescription(description, "请查看枚举:" + enumClazzName);
        }

        // 添加值描述
        if (CollectionUtils.isNotEmpty(descList)) {
            String valueDesc = "值说明:<br/>" + String.join("<br/>", descList) + "<br/>";
            appendToDescription(description, valueDesc);
        }

        return description.toString();
    }

    /**
     * 向描述中追加文本的辅助方法
     */
    private static void appendToDescription(StringBuilder description, String text) {
        if (StringUtils.isNotBlank(text)) {
            if (description.length() > 0) {
                description.append(",");
            }
            description.append(text);
        }
    }

    /**
     * 枚举处理结果
     */
    private static class EnumProcessingResult {
        final Class<?> valueClass;
        final AllowableListValues allowableListValues;
        final List<String> descList;

        EnumProcessingResult(Class<?> valueClass, AllowableListValues allowableListValues, List<String> descList) {
            this.valueClass = valueClass;
            this.allowableListValues = allowableListValues;
            this.descList = descList;
        }
    }

    private static class Result {
        /**
         * 参数类型
         */
        final Class<?> classType;
        /**
         * 允许的值
         */
        final AllowableListValues allowableListValues;
        /**
         * 最终描述
         */
        final String description;

        Result(Class<?> classType, AllowableListValues allowableListValues, String description) {
            this.classType = classType;
            this.allowableListValues = allowableListValues;
            this.description = description;
        }
    }

    /**
     * 根据原始类型和注解获取枚举类
     */
    private static Class<?> getEnumClazz(Class<?> rawType, ApiEnumProperty apiEnumProperty) {
        return Objects.equals(apiEnumProperty.enumClazz(), BaseEnum.class) ? rawType : apiEnumProperty.enumClazz();
    }


    @Override
    public boolean supports(DocumentationType documentationType) {
        return true;
    }

    @Override
    public void apply(ParameterContext parameterContext) {
        try {
            ApiEnumProperty apiEnumProperty = parameterContext.resolvedMethodParameter()
                    .findAnnotation(ApiEnumProperty.class)
                    .orElse(null);
            if (apiEnumProperty == null) {
                return;
            }

            ResolvedType parameterType = parameterContext.resolvedMethodParameter().getParameterType();
            Class<?> rawType = parameterType.getErasedType();

            Result result = buildEnumResult(rawType, apiEnumProperty);
            if (result == null) {
                return;
            }

            applyToParameterBuilder(parameterContext, apiEnumProperty, result);
        } catch (Exception e) {
            log.error("重建swagger参数描述失败", e);
        }
    }

    /**
     * 将结果应用到参数构建器
     */
    private void applyToParameterBuilder(ParameterContext parameterContext,
                                         ApiEnumProperty apiEnumProperty,
                                         Result result) {
        parameterContext.parameterBuilder()
                .description(result.description)
                .required(apiEnumProperty.required())
                .type(result.classType != null ? typeResolver.resolve(result.classType) : null)
                .scalarExample(StringUtils.isNotBlank(apiEnumProperty.example()) ? apiEnumProperty.example() : null)
                .name(StringUtils.isNotBlank(apiEnumProperty.name()) ? apiEnumProperty.name() : null)
                .allowableValues(result.allowableListValues);
    }
}