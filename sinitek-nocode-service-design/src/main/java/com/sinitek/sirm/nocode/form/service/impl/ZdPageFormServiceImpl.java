package com.sinitek.sirm.nocode.form.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.JsonArray;
import com.alibaba.nacos.shaded.com.google.gson.JsonElement;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.reflect.TypeToken;
import com.sinitek.data.model.version.enumerate.VersionStatusEnum;
import com.sinitek.data.model.version.util.VersionUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormDAO;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormTransDAO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormUpdateDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageForm;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormHistory;
import com.sinitek.sirm.nocode.form.mapper.ZdPageFormMapper;
import com.sinitek.sirm.nocode.form.service.IZdPageFormConfigService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormFieldMappingService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormHistoryService;
import com.sinitek.sirm.nocode.form.service.IZdPageFormService;
import com.sinitek.sirm.nocode.page.dto.PageDataDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.event.PageCreateEvent;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025-03-12 11:04:49
 * @description 针对表【zd_page_form(页面表单表)】的数据库操作Service实现
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ZdPageFormServiceImpl extends BaseDAO<ZdPageFormMapper, ZdPageForm, ZdPageFormDAO>
        implements IZdPageFormService {

    private Gson GSON = new Gson();
    private final IZdPageFormConfigService pageFormConfigService;
    private final IZdPageFormHistoryService pageFormHistoryService;
    private final IZdPageFormFieldMappingService pageFormFieldMappingService;


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> pageCodeList = appDeleteEvent.getCodeList();
        if (CollectionUtils.isEmpty(pageCodeList)) {
            return;
        }
        LambdaQueryWrapper<ZdPageForm> queryWrapper = eqOrIn(ZdPageForm::getPageCode, pageCodeList)
                .select(ZdPageForm::getId, ZdPageForm::getCode);
        List<String> formCodeList = new ArrayList<>();
        List<Long> formIdList = new ArrayList<>();
        dao.list(queryWrapper).forEach(p -> {
            formCodeList.add(p.getCode());
            formIdList.add(p.getId());
        });
        if (CollectionUtils.isNotEmpty(formCodeList)) {
            SpringUtil.publishEvent(new AppDeleteEvent(AppConstant.DE_FORM_CONFIG, formCodeList, formIdList));
        }
        dao.remove(queryWrapper);
        log.info("删除表单成功！");
    }

    /**
     * 创建页面后创建表单
     *
     * @param pageCreateEvent 创建页面事件
     */
    @Transactional(rollbackFor = Exception.class)
    @EventListener(condition = "T(com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum).isForm(#pageCreateEvent.source.pageType)")
    public void createForm(PageCreateEvent pageCreateEvent) {
        ZdPageDTO source = pageCreateEvent.getSource();
        String code = source.getCode();
        ZdPageForm zdPageForm = new ZdPageForm();
        zdPageForm.setPageCode(code);
        zdPageForm.setCode(code);
        dao.saveVersion(zdPageForm);
        // 这里需要创建一个表单配置，用来存储表单的配置信息，比如:表的名称
        pageFormConfigService.createFormConfig(code, source.getPageType(), source.getName());
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveOrUpdate(ZdPageFormUpdateDTO pageFormDTO) {
        // 只有未发布的表单才会新增或者修改
        // 同一时间，未发布的只有一条
        ZdPageForm zdPageForm = getNoPublish(pageFormDTO.getCode());
        if (Objects.isNull(zdPageForm)) {
            // todo
            throw new BussinessException("");
        }
        Long id = zdPageForm.getId();
        // 状态一定是未发布的
        Integer publishStatus = zdPageForm.getPublishStatus();
        if (!VersionUtil.isPublishAvailable(publishStatus)) {
            // todo
            throw new BussinessException("");
        }
        String newPageData = pageFormDTO.getPageData();
        String pageData = zdPageForm.getPageData();
        // 数据不一样
        if (!Objects.equals(newPageData, pageData)) {
            // 之前的不为空的话，需要保存历史
            if (StringUtils.isNotBlank(pageData)) {
                ZdPageFormHistory zdPageFormHistory = new ZdPageFormHistory();
                zdPageFormHistory.setPageFormId(zdPageForm.getId());
                zdPageFormHistory.setPageData(pageData);
                pageFormHistoryService.create(zdPageFormHistory);
            }
            // 更改表单数据
            LambdaUpdateWrapper<ZdPageForm> lu = lu().set(ZdPageForm::getPageData, newPageData)
                    .eq(ZdPageForm::getId, id);
            return dao.update(lu);
        }
        return true;
    }

    @Override
    public ZdPageFormDTO view(String code) {

        // 获取最新的一条数据
        LambdaUpdateWrapper<ZdPageForm> query = lu().eq(ZdPageForm::getCode, code)
                .eq(ZdPageForm::getThreadLatestFlag, 1);
        ZdPageForm zdPageForm = dao.getOne(query);
        if (Objects.isNull(zdPageForm)) {
            return null;
        }
        Integer publishStatus = zdPageForm.getPublishStatus();
        // 只有未发布和已发布的，才可以预览
        if (VersionUtil.isPublishAvailable(publishStatus) || VersionUtil.isUpdateVersionAvailable(publishStatus)) {
            return zdPageForm;
        }
        return null;
    }

    @Override
    public ZdPageFormDTO getPublishedForm(String code) {
        // 获取发布的表单
        LambdaQueryWrapper<ZdPageForm> query = eqOrIn(ZdPageForm::getCode, code)
                .eq(ZdPageForm::getPublishStatus, VersionStatusEnum.PUBLISHED.getValue());
        return dao.getOne(query);
    }

    @Override
    public ZdPageFormDTO getFormPageData(String code) {
        // 获取发布的表单
        ZdPageFormTransDAO zdPageFormTransDAO = new ZdPageFormTransDAO();
        LambdaQueryWrapper<ZdPageForm> query = eqOrIn(ZdPageForm::getCode, code);
//                .eq(ZdPageForm::getPublishStatus, VersionStatusEnum.PUBLISHED.getValue());
        ZdPageForm one = dao.getOne(query);
        if (Objects.isNull(one)) {
            return zdPageFormTransDAO;
        }
        /**
         * 应前端要求,翻译字段
         *
         * 注意: 当zd_page_form表的page_data格式发生大改变时,需要修改翻译逻辑
         */
        List<PageDataDTO> dataDTOList = new ArrayList<>();
        String pageData = one.getPageData();
        if (StringUtils.isNotBlank(pageData)) {
            JsonObject jsonObject = GSON.fromJson(pageData, JsonObject.class);
            JsonArray children = jsonObject.getAsJsonArray("children");
            translatePageData(dataDTOList, children);
        }
        BeanUtils.copyProperties(one, zdPageFormTransDAO);
        zdPageFormTransDAO.setPageDataDTOList(dataDTOList);
        //加快速度
        zdPageFormTransDAO.setPageData(null);
        return zdPageFormTransDAO;
    }

    private void translatePageData(List<PageDataDTO> dataDTOList, JsonArray children) {
        if (Objects.isNull(children)) {
            return;
        }
        for (int i = 0; i < children.size(); i++) {
            JsonArray children1 = children.get(i).getAsJsonObject().getAsJsonArray("children");
            if (Objects.isNull(children1)) {
                return;
            }
            for (int j = 0; j < children1.size(); j++) {
                JsonElement jsonElement = children1.get(j);
                handleElement(dataDTOList, jsonElement);
                //子表单
                JsonArray children2 = jsonElement.getAsJsonObject().getAsJsonArray("children");
                if (Objects.nonNull(children2)) {
                    for (int k = 0; k < children2.size(); k++) {
                        JsonElement element = children2.get(k);
                        handleElement(dataDTOList, element);
                    }
                }
            }
        }
    }

    private void handleElement(List<PageDataDTO> dataDTOList, JsonElement jsonElement) {
        PageDataDTO pageDataDTO = new PageDataDTO();
        JsonObject asJsonObject = jsonElement.getAsJsonObject();
        String ref = asJsonObject.get("ref").getAsString();
        pageDataDTO.setRef(ref);
        JsonObject props = asJsonObject.getAsJsonObject("props");
        String componentName = asJsonObject.get("componentName").getAsString();
        pageDataDTO.setComponentName(componentName);
        JsonElement label = props.get("label");
        if (Objects.nonNull(label)) {
            String labelName = label.getAsString();
            pageDataDTO.setLabel(labelName);
        }
        JsonArray options = props.getAsJsonArray("options");
        if (Objects.nonNull(options)) {
            List<Map<String, String>> result = GSON.fromJson(
                    options.toString(),
                    new TypeToken<List<Map<String, String>>>() {
                    }.getType()
            );
            pageDataDTO.setOptions(result);
        }
        dataDTOList.add(pageDataDTO);
    }

    /**
     * 获取未发布的表单
     *
     * @param formCode 表单编码
     * @return 未发布的表单
     */
    private ZdPageForm getNoPublish(String formCode) {
        LambdaQueryWrapper<ZdPageForm> queryWrapper = eqOrIn(ZdPageForm::getCode, formCode)
                .eq(ZdPageForm::getPublishStatus, VersionStatusEnum.UN_PUBLISHED.getValue());
        return dao.getOne(queryWrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZdPageFormDTO updateVersion(Long id) {
        return dao.updateVersion(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publish(String code) {
        // 只有未发布的表单才可以发布
        ZdPageForm zdPageForm = getNoPublish(code);
        dao.publish(zdPageForm.getId());
        // 发布完后，做字段映射
        pageFormFieldMappingService.saveOrUpdate(zdPageForm.getCode(), zdPageForm.getPageData());

    }

    @Override
    public void effectVersion(ZdPageFormDTO zdPageFormDTO) {
        ZdPageForm zdPageForm = new ZdPageForm();
        BeanUtils.copyProperties(zdPageFormDTO, zdPageForm);
        dao.effectVersion(zdPageForm);
    }

    @Override
    public ZdPageFormDTO applyHistoryVersion(Long id) {
        return dao.applyHistoryVersion(id);
    }

    @Override
    public List<ZdPageFormDTO> versionHistory(Long id) {
        return ConvertUtil.list(dao.findVersions(id));
    }

    @Override
    public List<ZdPageFormHistoryDTO> updateHistory(Long id) {
        return pageFormHistoryService.findByFormId(id);
    }

    @Override
    public boolean codeBelongsToAppCode(String formCode, String appCode) {
        return BooleanUtils.isTrue(getBaseMapper().codeBelongsToAppCode(formCode, appCode));
    }

    @Override
    public boolean exists(String formCode) {
        return exists(ZdPageForm::getCode, formCode);
    }

    @Override
    public boolean isPublished(String formCode) {
        LamWrapper<ZdPageForm> query = LamWrapper.eqOrIn(ZdPageForm::getCode, formCode)
                .eq(ZdPageForm::getPublishStatus, VersionStatusEnum.PUBLISHED.getValue());
        return exists(query);
    }
}




