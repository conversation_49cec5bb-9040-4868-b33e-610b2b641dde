package com.sinitek.sirm.nocode.page.controller;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.data.model.tree.enumerate.TypeEnum;
import com.sinitek.sirm.common.dto.OptionDTO;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.appmanager.aspect.ZdAppRight;
import com.sinitek.sirm.nocode.common.annotation.ApiEnumProperty;
import com.sinitek.sirm.nocode.common.dto.ZdCodeDTO;
import com.sinitek.sirm.nocode.common.dto.ZdIdDTO;
import com.sinitek.sirm.nocode.form.constant.FormConstant;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSaveDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageAuthSearchParamDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageBaseSettingDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCodeAndAppCodeDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCustomUrlDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageGroupSaveDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSaveDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSaveResultDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSceneDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageUpdateNameDTO;
import com.sinitek.sirm.nocode.page.enumerate.CustomUrlTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageAuthTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageAuthService;
import com.sinitek.sirm.nocode.page.service.IZdPageBaseSettingService;
import com.sinitek.sirm.nocode.page.service.IZdPageSceneService;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0312
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/page", tags = "页面管理接口")
@RequestMapping("/frontend/api/nocode/page")
@RequiredArgsConstructor
public class ZdAppPageController {

    private final IZdPageService pageService;


    private final IZdPageAuthService pageAuthService;
    private final IZdPageBaseSettingService pageBaseSettingService;
    private final IZdPageSceneService pageSceneService;


    @ZdAppRight(AppConstant.APP_CODE_PARAM)
    @ApiOperation(value = "新建表单（页面）")
    @PostMapping("/save")
    public RequestResult<ZdPageSaveResultDTO> save(
            @ApiParam(name = "保存页面参数", value = "保存页面参数")
            @Valid
            @RequestBody ZdPageSaveDTO pageSaveDTO
    ) {

        ZdPageDTO zdPageDTO = new ZdPageDTO();
        BeanUtils.copyProperties(pageSaveDTO, zdPageDTO);
        return new RequestResult<>(pageService.savePage(zdPageDTO));
    }

    @ZdAppRight(AppConstant.APP_CODE_PARAM)
    @ApiOperation(value = "保存(新增)分组")
    @PostMapping("/save-group")
    public RequestResult<ZdPageSaveResultDTO> saveGroup(
            @ApiParam(name = "保存分组", value = "保存分组")
            @Valid
            @RequestBody ZdPageGroupSaveDTO groupSaveDTO
    ) {

        ZdPageDTO zdPageDTO = new ZdPageDTO();
        BeanUtils.copyProperties(groupSaveDTO, zdPageDTO);
        // 设置页面类型为分组
        zdPageDTO.setPageType(PageTypeEnum.GROUP_PAGE);
        return new RequestResult<>(pageService.savePage(zdPageDTO));
    }


    @ApiOperation(value = "通过应用编码获取分组列表", notes = "这里只是获取顶级分组")
    @GetMapping("/group-list")
    public RequestResult<List<OptionDTO<Long>>> groupList(
            @ApiParam(value = "应用编码", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(pageService.groupList(appCode));
    }

    @ApiOperation(value = "通过应用编码获取分组树状结构")
    @GetMapping("/group-tree")
    public RequestResult<List<ZdPageDTO>> groupTree(
            @ApiParam(value = "应用编码", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(pageService.groupTree(appCode));
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.code)")
    @ApiOperation(value = "修改名称")
    @PostMapping("/update-name")
    public RequestResult<Boolean> updateName(
            @ApiParam(value = "页面名称修改参数")
            @Valid
            @RequestBody ZdPageUpdateNameDTO updateNameDTO
    ) {

        return new RequestResult<>(pageService.updateName(updateNameDTO));
    }

    @ApiOperation(value = "获取页面名称")
    @GetMapping("/get-name")
    public RequestResult<String> getNameByCode(
            @ApiParam(name = FormConstant.FORM_CODE, value = "页面编码", example = "page_57049cf9c4e94acfbc94d6775bc9e73e", required = true)
            @RequestParam(FormConstant.FORM_CODE) String pageCode
    ) {
        return new RequestResult<>(pageService.getNameByCode(pageCode));
    }


    @ApiOperation(value = "获取页面自定义url")
    @GetMapping("/get-custom-url")
    public RequestResult<String> getUrlByCode(
            @ApiParam(name = FormConstant.FORM_CODE, value = "页面编码", example = "page_57049cf9c4e94acfbc94d6775bc9e73e", required = true)
            @RequestParam(FormConstant.FORM_CODE) String pageCode
    ) {
        return new RequestResult<>(pageService.getUrlByCode(pageCode));
    }

    @ApiOperation(value = "通过自定义地址获取信息")
    @GetMapping("/get-info-by-url")
    public RequestResult<ZdPageCodeAndAppCodeDTO> getInfoByCustomUrl(
            @ApiParam(value = "自定义地址", example = "abcdef-url", required = true)
            @RequestParam("url") String url,
            @ApiEnumProperty(required = true, description = "地址类型", viewEnum = false)
            @RequestParam("type") CustomUrlTypeEnum type

    ) {
        return new RequestResult<>(pageService.getByUrl(url, type));
    }

    /**
     * 删除页面
     *
     * @param codeDTO 页面编码
     * @return 删除结果
     */
    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.code)")
    @ApiOperation(value = "删除页面", notes = "需要传递页面编码")
    @PostMapping("/delete")
    public RequestResult<Boolean> delete(
            @ApiParam(name = "删除页面参数", example = "{\"code\":\"page_114b522908e04cd09deeea7f4c25ca25\"}")
            @Valid
            @RequestBody ZdCodeDTO codeDTO
    ) {
        return new RequestResult<>(pageService.deleteByCode(codeDTO.getCode()));
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.code)")
    @ApiOperation(value = "批量删除页面")
    @PostMapping("/delete-batch")
    public RequestResult<Boolean> deleteBatch(
            @ApiParam(name = "删除页面参数", value = "页面页面参数，传递code")
            @Valid
            @RequestBody List<String> codeList
    ) {
        return new RequestResult<>(pageService.deleteByCode(codeList));
    }

    @ZdAppRight
    @ApiOperation(value = "页面列表", notes = "查询某个应用下的页面信息，既该应用下的左侧菜单")
    @GetMapping("/list")
    public RequestResult<List<ZdPageDTO>> list(
            @ApiParam(name = AppConstant.APP_CODE, value = "应用编码", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode,
            @ApiParam(name = "type", value = "类型,0:表示右侧下拉，1:表示下拉", required = false)
            @RequestParam(value = "type", defaultValue = "0") Integer type
    ) {
        return new RequestResult<>(pageService.listTree(appCode, type));
    }

    @ZdAppRight(checkStatus = false)
    @ApiOperation(value = "获取指定应用下的第一个表单code", notes = "查询某个应用下的页面默认第一个表单")
    @GetMapping("/get-first-form-code")
    public RequestResult<String> getFirstFormCode(
            @ApiParam(name = AppConstant.APP_CODE, value = "应用编码", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(pageService.getDefaultFormCode(appCode));
    }

    @ZdAppRight
    @ApiOperation(value = "所有表单", notes = "查询某个应用下的所有表单")
    @GetMapping("/forms")
    public RequestResult<List<Map<String, String>>> getAllForm(
            @ApiParam(name = AppConstant.APP_CODE, value = "应用编码", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode
    ) {
        return new RequestResult<>(pageService.getAllForm(appCode));
    }

    @ZdAppRight(value = "@appRightEl.getAppCodeByPageId(#p0)")
    @ApiOperation(value = "移动页面")
    @GetMapping("/move")
    public RequestResult<List<ZdPageDTO>> move(
            @ApiParam(name = "moveId", value = "移动页面的id")
            @RequestParam("moveId") Long moveId,
            @ApiParam(name = "targetId", value = "基准节点id")
            @RequestParam("targetId") Long targetId,
            @ApiEnumProperty(description = "移动方向", enumClazz = TypeEnum.class, labelKey = "name", viewEnum = false)
            @RequestParam("type") Integer type
    ) {
        RequestResult<List<ZdPageDTO>> requestResult = new RequestResult<>();
        try {
            List<ZdPageDTO> pageDTOList = pageService.move(moveId, targetId, type);
            requestResult.setData(pageDTOList);
        } catch (Exception e) {
            requestResult.setMessage(e.getMessage());
            requestResult.setResultcode(RequestResult.FAIL_CODE);
        }
        return requestResult;
    }


    @ApiOperation(value = "页面自定义url")
    @PostMapping("/custom-url")
    public RequestResult<String> customUrl(
            @ApiParam(name = "页面自定义urlDTOList", value = "是个数组，可以批量修改")
            @RequestBody List<ZdPageCustomUrlDTO> pageCustomUrlList
    ) {
        pageService.savePageCustomUrl(pageCustomUrlList, CurrentUserFactory.getOrgId());
        return new RequestResult<>();
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.pageCode)")
    @ApiOperation(value = "保存页面基本设置")
    @PostMapping("/save-base-setting")
    public RequestResult<Boolean> saveBaseSetting(
            @ApiParam(name = "页面基本设置参数", value = "页面基本设置参数")
            @Valid
            @RequestBody ZdPageBaseSettingDTO pageBaseSettingDTO
    ) {
        return new RequestResult<>(pageBaseSettingService.saveOrUpdate(pageBaseSettingDTO));
    }

    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0)")
    @ApiOperation(value = "获取页面基本设置")
    @GetMapping("/get-base-setting")
    public RequestResult<ZdPageBaseSettingDTO> getBaseSetting(
            @ApiParam(name = FormConstant.FORM_CODE, value = "表单编码", required = true, example = "page_114b522908e04cd09deeea7f4c25ca25")
            @RequestParam(FormConstant.FORM_CODE) String pageCode
    ) {
        return new RequestResult<>(pageBaseSettingService.getByPageCode(pageCode));
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.pageCode)")
    @ApiOperation(value = "保存或者修改权限")
    @PostMapping("/save-or-update-page-auth")
    public RequestResult<Long> saveOrUpdatePageAuth(
            @ApiParam(name = "页面权限参数", value = "页面权限参数")
            @Valid
            @RequestBody ZdPageAuthSaveDTO pageAuthDTO
    ) {
        return new RequestResult<>(pageAuthService.saveOrUpdate(pageAuthDTO));
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageAuthId(#p0.id)")
    @ApiOperation(value = "复制权限")
    @PostMapping("/copy-page-auth")
    public RequestResult<Long> copyPageAuth(
            @ApiParam(name = "权限id", value = "权限id")
            @Validated(value = {ZdIdDTO.Update.class})
            @RequestBody ZdIdDTO pageAuthDTO
    ) {
        return new RequestResult<>(pageAuthService.copyPageAuth(pageAuthDTO.getId()));
    }

    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.pageCode)")
    @ApiOperation(value = "权限列表")
    @PostMapping("/search-page-auth-list")
    public TableResult<ZdPageAuthSaveDTO> searchPageAuthList(
            @RequestBody
            @Valid
            @ApiParam(value = "权限查询条件", required = true) ZdPageAuthSearchParamDTO param
    ) {
        return pageAuthService.search(param);
    }


    /**
     * 根据权限id 删除权限
     *
     * @param idDTO 权限主键
     * @return 删除结果
     */
    @ZdAppRight(value = "@appRightEl.getAppCodeByPageAuthId(#p0.id)")
    @ApiOperation(value = "删除权限", notes = "需要传递权限主键id")
    @PostMapping("/delete-page-auth")
    public RequestResult<Boolean> deletePageAuth(
            @ApiParam(name = "权限id", value = "权限id")
            @Validated(value = {ZdIdDTO.Update.class})
            @RequestBody ZdIdDTO idDTO
    ) {
        return new RequestResult<>(pageAuthService.removeById(idDTO.getId()));
    }

    //@ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0)")
    @ApiOperation(value = "获取当前登陆人的权限")
    @GetMapping("/get-page-auth")
    public RequestResult<ZdPageAuthDTO> getPageAuth(
            @RequestParam(name = FormConstant.FORM_CODE)
            @ApiParam(name = FormConstant.FORM_CODE, value = "表单编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
            String pageCode,
            @RequestParam(name = "authType")
            @ApiEnumProperty(required = true, description = "权限类型")
            PageAuthTypeEnum authType
    ) {
        ZdPageAuthDTO zdPageAuthDTO = pageAuthService.rightQuery(pageCode, authType, CurrentUserFactory.getOrgId());
        if (Objects.isNull(zdPageAuthDTO)) {
            // 抛出没有权限
            throw new BussinessException("3000005");
        }
        return new RequestResult<>(zdPageAuthDTO);
    }


    @ApiOperation(value = "获取当前人权限字段")
    @GetMapping("/get-field-auth-data")
    public RequestResult<String> getFieldAuthData(
            @RequestParam(name = FormConstant.FORM_CODE)
            @ApiParam(name = FormConstant.FORM_CODE, value = "表单编码", example = "page_114b522908e04cd09deeea7f4c25ca25", required = true)
            String pageCode,
            @RequestParam(name = "authType")
            @ApiEnumProperty(required = true, description = "权限类型")
            PageAuthTypeEnum authType
    ) {
        ZdPageAuthDTO zdPageAuthDTO = pageAuthService.rightQuery(pageCode, authType, CurrentUserFactory.getOrgId());
        return new RequestResult<>(Objects.isNull(zdPageAuthDTO) ? null : zdPageAuthDTO.getFieldAuthData());
    }


    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0.pageCode)")
    @ApiOperation(value = "保存或者修改场景")
    @PostMapping("/save-or-update-page-scene")
    public RequestResult<Boolean> saveOrUpdatePageScene(
            @ApiParam(name = "场景参数", value = "场景参数")
            @Valid
            @RequestBody ZdPageSceneDTO pageSceneSaveDTO
    ) {
        return new RequestResult<>(pageSceneService.saveOrUpdate(pageSceneSaveDTO));
    }

    @ZdAppRight(value = "@appRightEl.getAppCodeByPageCode(#p0)")
    @ApiOperation(value = "通过页面code获取场景")
    @GetMapping("/get-page-scene")
    public RequestResult<ZdPageSceneDTO> getPageScene(
            @ApiParam(name = FormConstant.FORM_CODE, value = "页面编码", required = true, example = "page_114b522908e04cd09deeea7f4c25ca25")
            @RequestParam(name = FormConstant.FORM_CODE) String pageCode

    ) {
        return new RequestResult<>(pageSceneService.getPageScene(pageCode));
    }

    @ApiOperation(value = "通过编码获取场景类型", notes = "只有有提交权限，并且有人数限定的人员才能可以是收集表单")
    @GetMapping("/scene-type-list")
    public RequestResult<List<OptionDTO<Integer>>> sceneTypeList(
            @ApiParam(name = FormConstant.FORM_CODE, value = "页面编码", required = true, example = "page_114b522908e04cd09deeea7f4c25ca25")
            @RequestParam(name = FormConstant.FORM_CODE) String pageCode

    ) {
        return new RequestResult<>(pageSceneService.sceneTypeList(pageCode));
    }


    @JsonIgnore
    //@ApiOperation(value = "更新codeVal")
    @PostMapping("/update-code-val")
    public void updateCodeVal() {
        pageService.updateCodeVal();

    }


}
