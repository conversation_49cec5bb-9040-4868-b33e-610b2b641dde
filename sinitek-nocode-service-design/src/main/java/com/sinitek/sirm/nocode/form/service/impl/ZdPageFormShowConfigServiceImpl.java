package com.sinitek.sirm.nocode.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormShowConfigDAO;
import com.sinitek.sirm.nocode.form.dto.ZdFormDataComponentConfigDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormShowConfigDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormShowConfig;
import com.sinitek.sirm.nocode.form.mapper.ZdPageFormShowConfigMapper;
import com.sinitek.sirm.nocode.form.service.IZdPageFormShowConfigService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025-03-18 13:52:06
 * @description 针对表【zd_page_form_show_config(表单数据显示设置)】的数据库操作Service实现
 */
@Service
@Slf4j
public class ZdPageFormShowConfigServiceImpl extends BaseDAO<ZdPageFormShowConfigMapper, ZdPageFormShowConfig, ZdPageFormShowConfigDAO>
        implements IZdPageFormShowConfigService {


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM_CONFIG + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> formCodeList = appDeleteEvent.getCodeList();
        if (CollectionUtils.isEmpty(formCodeList)) {
            return;
        }
        LambdaQueryWrapper<ZdPageFormShowConfig> queryWrapper = eqOrIn(ZdPageFormShowConfig::getFormCode, formCodeList);
        dao.remove(queryWrapper);
        log.info("表单显示配置删除成功！");
    }

    @Override
    public void saveOrUpdate(ZdPageFormShowConfigDTO pageFormShowConfigDTO) {
        String orgId = pageFormShowConfigDTO.getOrgId();
        String formCode = pageFormShowConfigDTO.getFormCode();
        LambdaUpdateWrapper<ZdPageFormShowConfig> eq = lu()
                .set(ZdPageFormShowConfig::getShowConfig, pageFormShowConfigDTO.getShowConfig())
                .eq(ZdPageFormShowConfig::getFormCode, formCode)
                .eq(ZdPageFormShowConfig::getOrgId, orgId);
        if (!dao.update(eq)) {
            ZdPageFormShowConfig zdPageFormShowConfig = new ZdPageFormShowConfig();
            zdPageFormShowConfig.setFormCode(formCode);
            zdPageFormShowConfig.setOrgId(orgId);
            zdPageFormShowConfig.setShowConfig(pageFormShowConfigDTO.getShowConfig());
            dao.save(zdPageFormShowConfig);
        }
    }

    @Override
    public ZdPageFormShowConfigDTO getByFormCode(String formCode, String orgId) {
        LambdaQueryWrapper<ZdPageFormShowConfig> queryWrapper = eqOrIn(ZdPageFormShowConfig::getFormCode, formCode)
                .eq(ZdPageFormShowConfig::getOrgId, orgId);
        return dao.getOne(queryWrapper);
    }

    @Override
    public List<ZdFormDataComponentConfigDTO> findShowConfigFormCode(String formCode, String orgId) {
        ZdPageFormShowConfigDTO showConfigDTO = getByFormCode(formCode, orgId);
        if (Objects.nonNull(showConfigDTO)) {
            String showConfig = showConfigDTO.getShowConfig();
            if (Objects.nonNull(showConfig)) {
                return JsonUtil.toJavaObjectList(showConfig, ZdFormDataComponentConfigDTO.class);
            }
        }
        return Collections.emptyList();
    }
}




