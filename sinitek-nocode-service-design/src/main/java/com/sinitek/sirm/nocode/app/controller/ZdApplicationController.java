package com.sinitek.sirm.nocode.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.frontend.support.TableResult;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dto.ZdAppBaseInfoDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppCustomUrlDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSaveDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSearchParamDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSecretDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppSettingDTO;
import com.sinitek.sirm.nocode.app.dto.ZdAppUpdateNameDTO;
import com.sinitek.sirm.nocode.app.service.IZdAppService;
import com.sinitek.sirm.nocode.appmanager.aspect.ZdAppRight;
import com.sinitek.sirm.nocode.common.dto.ZdCodeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025.0311
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@RestController
@Api(value = "/frontend/api/nocode/app", tags = "应用接口")
@RequestMapping("/frontend/api/nocode/app")
public class ZdApplicationController {
    @Resource
    private IZdAppService zdAppService;

    @ApiOperation(value = "查询应用列表")
    @PostMapping("/search")
    public TableResult<ZdAppDTO> search(@RequestBody
                                        @ApiParam("应用查询条件") ZdAppSearchParamDTO param
    ) {
        param.setOrgId(CurrentUserFactory.getOrgId());
        IPage<ZdAppDTO> pageData = zdAppService.search(param);
        return param.build(pageData);
    }


    @ApiOperation(value = "创建一个应用")
    @PostMapping("/create")
    public RequestResult<String> create(
            @ApiParam(name = "创建应用参数DTO", value = "创建应用时的参数", required = true)
            @Valid
            @RequestBody ZdAppSaveDTO zdAppSaveDTO) {
        zdAppSaveDTO.setCurrentOrgId(CurrentUserFactory.getOrgId());
        return new RequestResult<>(zdAppService.create(zdAppSaveDTO));
    }


    @ZdAppRight
    @ApiOperation(value = "修改应用的名称")
    @PostMapping("/update/name")
    public RequestResult<Boolean> updateName(
            @ApiParam(name = "修改应用名称参数，应用code必须传递", required = true)
            @Valid
            @RequestBody ZdAppUpdateNameDTO zdAppUpdateNameDTO) {
        return new RequestResult<>(zdAppService.updateName(zdAppUpdateNameDTO));
    }


    @ZdAppRight
    @ApiOperation(value = "修改应用")
    @PostMapping("/update")
    public RequestResult<Boolean> update(
            @ApiParam(name = "修改应用参数,应用code和id必须传递", required = true)
            @Valid
            @RequestBody ZdAppDTO zdAppSaveDTO) {
        return new RequestResult<>(zdAppService.update(zdAppSaveDTO));
    }

    @ZdAppRight(checkStatus = false)
    @ApiOperation(value = "删除应用")
    @PostMapping("/delete")
    public RequestResult<Boolean> delete(
            @ApiParam(name = "删除应用参数,需要传递应用code", required = true)
            @RequestBody ZdCodeDTO codeDTO) {
        return new RequestResult<>(zdAppService.deleteByCode(codeDTO.getCode()));
    }

    @ZdAppRight(checkStatus = false)
    @ApiOperation(value = "修改应用的状态")
    @PostMapping("/update/status")
    public RequestResult<Boolean> updateStatus(
            @ApiParam(name = "修改应用的状态参数,需要传递应用code", required = true)
            @Valid
            @RequestBody ZdCodeDTO codeDTO) {
        return new RequestResult<>(zdAppService.updateStatus(codeDTO.getCode()));
    }


    @ApiOperation(value = "获取应用的基本信息")
    @GetMapping("/get-base-info")
    public RequestResult<ZdAppBaseInfoDTO> getBaseInfo(
            @ApiParam(name = AppConstant.APP_CODE, value = "获取应用的基本信息,需要传递应用code", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode) {
        return new RequestResult<>(zdAppService.getBaseInfo(appCode, CurrentUserFactory.getOrgId()));
    }


    @ZdAppRight
    @ApiOperation(value = "查看应用密钥")
    @GetMapping("/view/secret")
    public RequestResult<ZdAppSecretDTO> viewSecret(
            @ApiParam(name = AppConstant.APP_CODE, value = "查看应用密钥参数,需要传递应用code", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
            @Valid
            @RequestParam(AppConstant.APP_CODE) String appCode) {
        return new RequestResult<>(zdAppService.viewSecret(appCode));
    }

    @ZdAppRight
    @ApiOperation(value = "应用设置")
    @GetMapping("/setting")
    public RequestResult<ZdAppSettingDTO> setting(
            @ApiParam(value = "需要传递应用编码", example = "app_48539ab4e34f478289d85a691e37661b", required = true)
            @RequestParam(AppConstant.APP_CODE) String appCode) {
        return new RequestResult<>(zdAppService.setting(appCode));
    }

    @ApiOperation(value = "访问地址设置")
    @PostMapping("/custom-url")
    public RequestResult<String> customUrl(
            @ApiParam(name = "页面自定义urlDTOList", value = "是个数组，可以批量修改")
            @RequestBody List<ZdAppCustomUrlDTO> appCustomUrlDTOList
    ) {
        zdAppService.savePageCustomUrl(appCustomUrlDTOList, CurrentUserFactory.getOrgId());
        return new RequestResult<>();
    }

}
