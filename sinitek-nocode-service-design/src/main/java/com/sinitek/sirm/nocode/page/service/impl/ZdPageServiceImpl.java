package com.sinitek.sirm.nocode.page.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.data.model.tree.enumerate.TypeEnum;
import com.sinitek.sirm.common.dto.OptionDTO;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dto.ZdAppSettingDTO;
import com.sinitek.sirm.nocode.app.entity.ZdApp;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.app.support.IZdAppSettingCustomizer;
import com.sinitek.sirm.nocode.common.dto.ZdBaseNodeEntity;
import com.sinitek.sirm.nocode.common.dto.ZdTreeDTO;
import com.sinitek.sirm.nocode.common.tree.TreeDataMaker;
import com.sinitek.sirm.nocode.common.utils.CodeCreateUtil;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import com.sinitek.sirm.nocode.page.dao.ZdPageDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCodeAndAppCodeDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCustomUrlDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSaveResultDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageUpdateNameDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPage;
import com.sinitek.sirm.nocode.page.enumerate.CustomUrlTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import com.sinitek.sirm.nocode.page.event.PageCreateEvent;
import com.sinitek.sirm.nocode.page.mapper.ZdPageMapper;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025-03-12 10:00:09
 * @description 针对表【zd_page(页面表)】的数据库操作Service实现
 */
@Service
public class ZdPageServiceImpl extends BaseDAO<ZdPageMapper, ZdPage, ZdPageDAO>
        implements IZdPageService, IZdAppSettingCustomizer {


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_PAGE + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> appCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdPage> queryWrapper = appCodeQuery(appCodeList).select(ZdPage::getCode);
        List<String> pageCodeList = dao.list(queryWrapper).stream().map(ZdPage::getCode).collect(Collectors.toList());
        delete(queryWrapper, pageCodeList);
    }

    /**
     * 删除页面
     *
     * @param queryWrapper 删除条件
     * @param pageCodeList codeList
     * @return 是否成功
     */
    private boolean delete(LambdaQueryWrapper<ZdPage> queryWrapper, List<String> pageCodeList) {
        if (CollectionUtils.isNotEmpty(pageCodeList)) {
            SpringUtil.publishEvent(new AppDeleteEvent(AppConstant.DE_FORM, pageCodeList));
        }
        // 删除页面
        return dao.remove(queryWrapper);
    }


    @Override
    public void customize(ZdAppSettingDTO zdAppSettingDTO, String appCode) {
        List<ZdPageCustomUrlDTO> pageCustomUrlList = zdAppSettingDTO.getPageCustomUrlList();
        if (pageCustomUrlList == null) {
            pageCustomUrlList = new ArrayList<>();
            zdAppSettingDTO.setPageCustomUrlList(pageCustomUrlList);
        } else {
            return;
        }
        List<ZdPage> list = dao.list(appCodeQuery(appCode).select(ZdPage::getId, ZdPage::getName, ZdPage::getUrl));
        list.forEach(page -> {
            ZdPageCustomUrlDTO zdPageCustomUrlDTO = new ZdPageCustomUrlDTO();
            zdPageCustomUrlDTO.setId(page.getId());
            zdPageCustomUrlDTO.setUrl(page.getUrl());
            zdPageCustomUrlDTO.setName(page.getName());
            zdAppSettingDTO.getPageCustomUrlList().add(zdPageCustomUrlDTO);
        });
    }


    private LambdaQueryWrapper<ZdPage> appCodeQuery(Object appCode) {
        return eqOrIn(ZdPage::getAppCode, appCode);
    }

    private LambdaQueryWrapper<ZdPage> codeQuery(Object code) {
        return eqOrIn(ZdPage::getCode, code);
    }

    @Override
    public boolean updateName(ZdPageUpdateNameDTO zdPageUpdateNameDTO) {
        String code = zdPageUpdateNameDTO.getCode();
        String name = zdPageUpdateNameDTO.getName();
        LambdaUpdateWrapper<ZdPage> updateWrapper = lu().set(StringUtils.isNotBlank(name), ZdPage::getName, name)
                .eq(ZdPage::getCode, code);
        return dao.update(updateWrapper);
    }

    @Override
    public String getNameByCode(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code).select(ZdPage::getName);
        ZdPage zdPage = dao.getOne(queryWrapper);
        if (Objects.nonNull(zdPage)) {
            return zdPage.getName();
        } else {
            return "";
        }
    }

    @Override
    public String getUrlByCode(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code).select(ZdPage::getUrl);
        ZdPage zdPage = dao.getOne(queryWrapper);
        if (Objects.nonNull(zdPage)) {
            return zdPage.getUrl();
        } else {
            return "";
        }
    }

    @Override
    public ZdPageCodeAndAppCodeDTO getByUrl(String url, CustomUrlTypeEnum type) {
        ZdPageCodeAndAppCodeDTO info = new ZdPageCodeAndAppCodeDTO();
        if (Objects.equals(type, CustomUrlTypeEnum.FORM)) {
            LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getUrl, url).select(ZdPage::getCode, ZdPage::getAppCode);
            ZdPage one = dao.getOne(queryWrapper);
            if (Objects.nonNull(one)) {
                info.setAppCode(one.getAppCode());
                info.setFormCode(one.getCode());
            }
        } else if (Objects.equals(type, CustomUrlTypeEnum.APP)) {
            LamWrapper<ZdApp> queryWrapper = LamWrapper.eqOrIn(ZdApp::getUrl, url).select(ZdApp::getCode);
            info.setAppCode(stringValue(queryWrapper));
        }
        return info;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZdPageSaveResultDTO savePage(ZdPageDTO pageDTO) {
        ZdPage zdPage = new ZdPage();
        CopyUtil.copyProperties(pageDTO, zdPage);
        // 设置code
        zdPage.setCode(CodeCreateUtil.createCode("page_"));
        Long parentId = zdPage.getParentId();
        ZdPage preNode = null;
        if (IdUtil.isDataId(parentId)) {
            preNode = dao.getById(parentId);
        }
        dao.addNode(preNode, zdPage, TypeEnum.SUB);
        // 发出创建页面事件
        SpringUtil.publishEvent(new PageCreateEvent(zdPage));
        return CopyUtil.copyProperties(zdPage, new ZdPageSaveResultDTO());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByCode(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code);
        ZdPage one = dao.getOne(queryWrapper);
        getGroupList(one.getAppCode());
        boolean groupHasForm = groupHasForm(one.getAppCode(), null, code);
        if (groupHasForm) {
            throw new BussinessException("3000016");
        }
        return delete(queryWrapper, Collections.singletonList(code));
    }

    private boolean groupHasForm(String appCode, Integer type, String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = order(appCodeQuery(appCode));
        Predicate<ZdTreeDTO<ZdPage>> predicate = null;
        if (Objects.equals(type, 1)) {
            predicate = ZdPageServiceImpl::filterEmptyGroup;
        }
        List<ZdPage> list = TreeDataMaker.tree(dao.list(queryWrapper), predicate);
        for (ZdPage m : list) {
            if (m.getCode().equals(code) && CollectionUtils.isNotEmpty(m.getChildren())) {
                return true;
            }
        }
        return false;
    }

    public List<OptionDTO<? extends List<? extends ZdBaseNodeEntity>>> getGroupList(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = appCodeQuery(appCode)
                .eq(ZdPage::getPageType, PageTypeEnum.GROUP_PAGE)
                .eq(ZdPage::getParentId, 0)
                .select(ZdPage::getId, ZdPage::getName);
        List<ZdPage> list = dao.list(order(queryWrapper));
        return list.stream().map(page -> new OptionDTO<>(page.getCode(), page.getChildren())).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByCode(List<String> codeList) {
        codeList.forEach(this::deleteByCode);
        return true;
    }

    @Override
    public boolean exists(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code);
        return dao.count(queryWrapper) > 0;
    }

    @Override
    public List<ZdPageDTO> listTree(String appCode, Integer type) {
        LambdaQueryWrapper<ZdPage> queryWrapper = order(appCodeQuery(appCode));
        Predicate<ZdTreeDTO<ZdPage>> predicate = null;
        if (Objects.equals(type, 1)) {
            predicate = ZdPageServiceImpl::filterEmptyGroup;
        }
        List<ZdPage> tree = TreeDataMaker.tree(dao.list(queryWrapper), predicate);
        return ConvertUtil.list(tree);
    }

    @Override
    public String getDefaultFormCode(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = order(appCodeQuery(appCode));
        LamWrapper<ZdPage> one = LamWrapper.ins(queryWrapper)
                .select(ZdPage::getCode)
                .ne(ZdPage::getPageType, PageTypeEnum.GROUP_PAGE)
                .one();
        String code = stringValue(one);
        if (Objects.isNull(code)) {
            return "";
        }
        return code;
    }

    @Override
    public List<Map<String, String>> getAllForm(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = order(appCodeQuery(appCode));
        queryWrapper.ne(ZdPage::getPageType, 9);
        List<ZdPage> list = dao.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .map(zdPage -> {
                        Map<String, String> map = new HashMap<>();
                        map.put("value", zdPage.getCode());
                        map.put("label", zdPage.getName());
                        return map;
                    }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private static boolean filterEmptyGroup(ZdTreeDTO<ZdPage> r) {
        ZdPage a = r.getTreeOrgData();
        PageTypeEnum pageType = a.getPageType();
        if (Objects.equals(pageType, PageTypeEnum.GROUP_PAGE)) {
            List<ZdTreeDTO<ZdPage>> children = r.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                return false;
            } else {
                return children.stream().anyMatch(ZdPageServiceImpl::filterEmptyGroup);
            }
        }
        return true;
    }

    @SneakyThrows
    @Override
    public List<ZdPageDTO> move(Long moveId, Long targetId, Integer type) {
        ZdPage moveNode = idCheck(moveId);
        ZdPage targetNode = idCheck(targetId);
        if (!PageTypeEnum.GROUP_PAGE.getValue().equals(targetNode.getPageType().getValue()) && type != 0) {
            //平级表单移动是被允许的
            throw new Exception("操作有误，无法移动至表单！");
        }
        if (!Objects.equals(moveNode.getAppCode(), targetNode.getAppCode())) {
            // todo 必须是同一个应用下才可以移动
            throw new BussinessException("");
        }
        TypeEnum typeEnum = TypeEnum.fromValue(type);
        dao.moveNode(moveNode, targetNode, typeEnum);
        return listTree(moveNode.getAppCode(), 0);
    }

    @Override
    public String getAppCodeByCode(List<String> pageCodeList) {
        LambdaQueryWrapper<ZdPage> eq = codeQuery(pageCodeList).select(ZdPage::getAppCode);
        List<ZdPage> pageList = dao.list(eq);
        if (CollectionUtils.isNotEmpty(pageList)) {
            // 需要去重
            return pageList.stream().map(ZdPage::getAppCode).distinct().collect(Collectors.joining(","));
        }
        return null;
    }

    @Override
    public String getAppCodeById(Long id) {
        LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getId, id)
                .select(ZdPage::getAppCode);
        ZdPage one = dao.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
            return one.getAppCode();
        }
        return null;
    }

    @Override
    public void savePageCustomUrl(List<ZdPageCustomUrlDTO> pageCustomUrlList, String orgId) {
        pageCustomUrlList.forEach(pageCustomUrlDTO -> dao.update(lu().set(ZdPage::getUrl, pageCustomUrlDTO.getUrl()).eq(ZdPage::getCode, pageCustomUrlDTO.getCode())));
    }

    @Override
    public List<OptionDTO<Long>> groupList(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = appCodeQuery(appCode)
                .eq(ZdPage::getPageType, PageTypeEnum.GROUP_PAGE)
                .eq(ZdPage::getParentId, 0)
                .select(ZdPage::getId, ZdPage::getName);
        List<ZdPage> list = dao.list(order(queryWrapper));
        return list.stream().map(page -> new OptionDTO<>(page.getName(), page.getId())).collect(Collectors.toList());
    }

    @Override
    public List<ZdPageDTO> groupTree(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = appCodeQuery(appCode)
                .eq(ZdPage::getPageType, PageTypeEnum.GROUP_PAGE)
                .select(ZdPage::getId, ZdPage::getName, ZdPage::getParentId, ZdPage::getSort);
        return ConvertUtil.list(TreeDataMaker.tree(dao.list(order(queryWrapper))));
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCodeVal() {
        LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getParentId, 0)
                .select(ZdPage::getId, ZdPageDTO::getCodeVal, ZdPage::getSort, ZdPageDTO::getCreateTimeStamp)
                .orderByAsc(ZdPage::getSort, ZdPageDTO::getCreateTimeStamp);
        List<ZdPage> list = dao.list(queryWrapper);
        for (int i = 0; i < list.size(); i++) {
            ZdPage zdPage = list.get(i);
            String codeVal = i + 1 + ";";
            changeChildCodeVal(zdPage.getId(), zdPage.getCodeVal(), codeVal);
            if (!Objects.equals(zdPage.getCodeVal(), codeVal)) {
                LambdaUpdateWrapper<ZdPage> updateQuery = Wrappers.<ZdPage>lambdaUpdate()
                        .set(ZdPage::getCodeVal, codeVal)
                        .eq(ZdPage::getId, zdPage.getId());
                dao.update(updateQuery);
            }
        }
    }

    private void changeChildCodeVal(Long parentId, String oldCodeVal, String newCodeVal) {
        if (!Objects.equals(oldCodeVal, newCodeVal)) {
            LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getParentId, parentId)
                    .select(ZdPage::getId, ZdPageDTO::getCodeVal);
            List<ZdPage> list = dao.list(queryWrapper);
            list.forEach(zdPage -> {
                // 需要替换第一个
                String codeVal1 = zdPage.getCodeVal();
                String codeVal2 = codeVal1.replaceFirst(oldCodeVal, newCodeVal);
                LambdaUpdateWrapper<ZdPage> updateQuery = Wrappers.<ZdPage>lambdaUpdate()
                        .set(ZdPage::getCodeVal, codeVal2)
                        .eq(ZdPage::getId, zdPage.getId());
                dao.update(updateQuery);
                changeChildCodeVal(zdPage.getId(), codeVal1, codeVal2);
            });
        }
    }


    private static LambdaQueryWrapper<ZdPage> order(LambdaQueryWrapper<ZdPage> queryWrapper) {
        queryWrapper.orderByAsc(ZdPage::getSort)
                .orderByDesc(ZdPage::getCreateTimeStamp);
        return queryWrapper;
    }


}




