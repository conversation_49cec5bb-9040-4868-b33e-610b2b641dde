# IntelliJ IDEA 性能优化指南

## 🚨 立即解决编译慢问题

### 1. 关闭代码覆盖率（最重要）
- **位置**：Run/Debug Configurations → Code Coverage
- **操作**：取消勾选所有覆盖率选项
- **影响**：可以提升编译速度50-80%

### 2. 调整IDEA内存设置
**Help → Change Memory Settings**
```
-Xms2048m
-Xmx8192m
-XX:ReservedCodeCacheSize=1024m
-XX:InitialCodeCacheSize=64m
```

### 3. 编译器优化
**File → Settings → Build, Execution, Deployment → Compiler**
- ✅ Build project automatically
- ✅ Compile independent modules in parallel
- 设置 "User-local build process heap size": 4096 MB
- 设置 "User-local build process VM options": `-XX:MaxMetaspaceSize=512m`

### 4. Maven配置优化
**File → Settings → Build Tools → Maven**
- **Maven home directory**: 使用本地Maven而非IDEA内置
- **User settings file**: 指定本地settings.xml
- **Local repository**: 确保指向正确的本地仓库
- **VM options for importer**: `-Xmx2048m -XX:MaxPermSize=512m`
- **Threads**: 设置为CPU核心数（如：-T 8）

### 5. 项目结构优化
**File → Project Structure**
- 确保所有模块的SDK版本一致
- 移除不必要的库依赖
- 检查模块依赖关系，避免循环依赖

## 🔧 高级优化配置

### 6. 禁用不必要的插件
**File → Settings → Plugins**
禁用以下插件（如果不需要）：
- Database Tools and SQL
- Kubernetes
- Docker
- Android Support
- Scala
- Kotlin（如果项目不使用）

### 7. 索引优化
**File → Settings → Advanced Settings**
- `ide.max.intellisense.filesize`: 2500 (KB)
- `compiler.automake.allow.when.app.running`: true
- `compiler.document.save.enabled`: false

### 8. 文件监控优化
**Help → Edit Custom VM Options**
添加以下参数：
```
-Dide.slow.operations.assertion=false
-Djb.vmOptionsFile.encoding=UTF-8
-Dfile.encoding=UTF-8
-Djava.system.class.loader=com.intellij.util.lang.PathClassLoader
-Xverify:none
```

### 9. 排除不必要的文件夹
**File → Settings → Editor → File Types**
在"Ignored Files and Folders"中添加：
```
*.iml;*.ipr;*.iws;
.idea;
target;
node_modules;
.git;
logs;
temp;
```

### 10. Maven并行编译
在项目根目录的`.mvn/maven.config`文件中添加：
```
-T 1C
-Dmaven.compile.fork=true
-Dmaven.compiler.maxmem=2048m
```

## 🎯 针对您项目的特殊优化

### 11. 多模块项目优化
由于您的项目是多模块结构，建议：

1. **分模块导入**：
   - 只导入当前开发的模块
   - 其他模块作为Maven依赖

2. **模块编译顺序**：
   ```xml
   <!-- 在根pom.xml中优化模块顺序 -->
   <modules>
       <module>sinitek-nocode-api</module>
       <module>sinitek-nocode-dal</module>
       <module>sinitek-nocode-service-design</module>
       <module>sinitek-nocode-service-llm</module>
       <module>sinitek-nocode-service-runtime</module>
       <module>sinitek-nocode-assembly</module>
   </modules>
   ```

### 12. 依赖优化
检查并优化Maven依赖：
- 移除重复依赖
- 使用dependencyManagement统一版本
- 排除不必要的传递依赖

### 13. 开发模式优化
**开发时建议**：
- 只启动需要的模块
- 使用Spring Boot DevTools热重载
- 关闭不必要的Spring Boot自动配置

## 📊 性能监控

### 14. 监控编译性能
**Help → Diagnostic Tools → Activity Monitor**
- 查看哪些进程占用资源最多
- 识别性能瓶颈

### 15. 内存使用监控
**View → Appearance → Status Bar Widgets → Memory Indicator**
- 实时监控内存使用
- 及时发现内存泄漏

## 🚀 预期效果

应用这些优化后，编译时间应该从30分钟降低到：
- **增量编译**：1-3分钟
- **全量编译**：5-10分钟
- **启动时间**：30秒-2分钟

## ⚡ 立即行动清单

1. [ ] 关闭代码覆盖率
2. [ ] 调整IDEA内存到8GB
3. [ ] 启用并行编译
4. [ ] 优化Maven配置
5. [ ] 禁用不必要的插件
6. [ ] 排除target等文件夹
7. [ ] 配置Maven并行编译

**注意**：请按顺序执行，每次修改后重启IDEA测试效果。
